# 汉字纹身AI平台 API 文档

## 基础信息

- **基础URL**: `http://localhost:8000/api/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`

## 认证接口

### 用户注册
```http
POST /auth/register
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "full_name": "Full Name"
}
```

**响应**:
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "username",
  "full_name": "Full Name",
  "is_active": true,
  "credits": 0,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 用户登录
```http
POST /auth/login
```

**请求体**:
```json
{
  "username": "username",
  "password": "password123"
}
```

**响应**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "username": "username",
    "email": "<EMAIL>"
  }
}
```

### 获取当前用户信息
```http
GET /auth/me
Authorization: Bearer <token>
```

## 汉字成语接口

### 搜索汉字
```http
GET /characters/search?q=love&limit=10
```

**响应**:
```json
{
  "characters": [
    {
      "id": 1,
      "character": "爱",
      "pinyin": "ài",
      "meaning_en": "love, affection",
      "meaning_zh": "爱，喜爱",
      "cultural_context": "表达深厚的情感...",
      "category": "emotion",
      "tattoo_popularity": 95
    }
  ],
  "total": 1
}
```

### 获取推荐汉字/成语
```http
POST /characters/recommend
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "keywords": ["strength", "wisdom", "peace"],
  "description": "I want something that represents inner strength and wisdom",
  "type": "both" // "character", "idiom", "both"
}
```

**响应**:
```json
{
  "recommendations": {
    "characters": [
      {
        "character": "智",
        "pinyin": "zhì",
        "meaning_en": "wisdom, intelligence",
        "score": 0.95,
        "explanation": "This character represents wisdom and intelligence..."
      }
    ],
    "idioms": [
      {
        "idiom": "自强不息",
        "pinyin": "zì qiáng bù xī",
        "meaning_en": "constantly strive to become stronger",
        "score": 0.92,
        "explanation": "This idiom means to constantly improve oneself..."
      }
    ]
  },
  "ai_explanation": "Based on your keywords, I recommend..."
}
```

## 纹身生成接口

### 创建纹身请求
```http
POST /tattoos/requests
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "keywords": ["strength", "wisdom"],
  "description": "I want a tattoo that represents inner strength",
  "selected_type": "character",
  "selected_content": "智",
  "preferred_style": "calligraphy",
  "preferred_position": "forearm",
  "size_preference": "medium"
}
```

### 生成纹身预览图
```http
POST /tattoos/generate
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "request_id": 1,
  "style": "calligraphy",
  "position": "forearm",
  "content": "智",
  "high_resolution": false
}
```

**响应**:
```json
{
  "image_id": 1,
  "image_url": "https://example.com/images/preview.jpg",
  "watermarked_url": "https://example.com/images/watermarked.jpg",
  "thumbnail_url": "https://example.com/images/thumb.jpg",
  "generation_time": 3.5,
  "is_high_resolution": false
}
```

### 解锁高清图片
```http
POST /tattoos/unlock/{image_id}
Authorization: Bearer <token>
```

## 用户接口

### 获取用户纹身历史
```http
GET /users/tattoo-history?page=1&limit=10
Authorization: Bearer <token>
```

### 更新用户资料
```http
PUT /users/profile
Authorization: Bearer <token>
```

## 支付接口

### 创建支付订单
```http
POST /payments/create-order
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "order_type": "credits",
  "amount": 9.99,
  "credits_amount": 100
}
```

### 确认支付
```http
POST /payments/confirm
Authorization: Bearer <token>
```

## 错误响应

所有错误响应都遵循以下格式：

```json
{
  "detail": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 常见错误码

- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 权限不足
- `404` - 资源不存在
- `429` - 请求频率限制
- `500` - 服务器内部错误
