{"version": 3, "file": "autocomplete2.js", "sources": ["../../../../../../packages/components/autocomplete/src/autocomplete.ts"], "sourcesContent": ["import {\n  NOOP,\n  buildProps,\n  definePropType,\n  isObject,\n  isString,\n} from '@element-plus/utils'\nimport { useTooltipContentProps } from '@element-plus/components/tooltip'\nimport { useAriaProps } from '@element-plus/hooks'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Autocomplete from './autocomplete.vue'\nimport type { Placement } from '@element-plus/components/popper'\nimport type { Awaitable } from '@element-plus/utils'\n\nexport type AutocompleteData = Record<string, any>[]\nexport type AutocompleteFetchSuggestionsCallback = (\n  data: AutocompleteData\n) => void\nexport type AutocompleteFetchSuggestions =\n  | ((\n      queryString: string,\n      cb: AutocompleteFetchSuggestionsCallback\n    ) => Awaitable<AutocompleteData> | void)\n  | AutocompleteData\n\nexport const autocompleteProps = buildProps({\n  /**\n   * @description key name of the input suggestion object for display\n   */\n  valueKey: {\n    type: String,\n    default: 'value',\n  },\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: [String, Number],\n    default: '',\n  },\n  /**\n   * @description debounce delay when typing, in milliseconds\n   */\n  debounce: {\n    type: Number,\n    default: 300,\n  },\n  /**\n   * @description placement of the popup menu\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: [\n      'top',\n      'top-start',\n      'top-end',\n      'bottom',\n      'bottom-start',\n      'bottom-end',\n    ],\n    default: 'bottom-start',\n  },\n  /**\n   * @description a method to fetch input suggestions. When suggestions are ready, invoke `callback(data:[])` to return them to Autocomplete\n   */\n  fetchSuggestions: {\n    type: definePropType<AutocompleteFetchSuggestions>([Function, Array]),\n    default: NOOP,\n  },\n  /**\n   * @description custom class name for autocomplete's dropdown\n   */\n  popperClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether show suggestions when input focus\n   */\n  triggerOnFocus: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether to emit a `select` event on enter when there is no autocomplete match\n   */\n  selectWhenUnmatched: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether to hide the loading icon in remote search\n   */\n  hideLoading: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether select dropdown is teleported to the body\n   */\n  teleported: useTooltipContentProps.teleported,\n  /**\n   * @description which select dropdown appends to\n   */\n  appendTo: useTooltipContentProps.appendTo,\n  /**\n   * @description whether to highlight first item in remote search suggestions by default\n   */\n  highlightFirstItem: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether the width of the dropdown is the same as the input\n   */\n  fitInputWidth: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether to show clear button\n   */\n  clearable: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether to disable\n   */\n  disabled: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description same as `name` in native input\n   */\n  name: String,\n  ...useAriaProps(['ariaLabel']),\n} as const)\nexport type AutocompleteProps = ExtractPropTypes<typeof autocompleteProps>\n\nexport const autocompleteEmits = {\n  [UPDATE_MODEL_EVENT]: (value: string) => isString(value),\n  [INPUT_EVENT]: (value: string) => isString(value),\n  [CHANGE_EVENT]: (value: string) => isString(value),\n  focus: (evt: FocusEvent) => evt instanceof FocusEvent,\n  blur: (evt: FocusEvent) => evt instanceof FocusEvent,\n  clear: () => true,\n  select: (item: Record<string, any>) => isObject(item),\n}\nexport type AutocompleteEmits = typeof autocompleteEmits\n\nexport type AutocompleteInstance = InstanceType<typeof Autocomplete> & unknown\n"], "names": ["buildProps", "definePropType", "NOOP", "useTooltipContentProps", "useAriaProps", "UPDATE_MODEL_EVENT", "isString", "INPUT_EVENT", "CHANGE_EVENT", "isObject"], "mappings": ";;;;;;;;;;AAcY,MAAC,iBAAiB,GAAGA,kBAAU,CAAC;AAC5C,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;AAC1B,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK;AACX,MAAM,WAAW;AACjB,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,cAAc;AACpB,MAAM,YAAY;AAClB,KAAK;AACL,IAAI,OAAO,EAAE,cAAc;AAC3B,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC3C,IAAI,OAAO,EAAEC,WAAI;AACjB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,mBAAmB,EAAE;AACvB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,UAAU,EAAEC,8BAAsB,CAAC,UAAU;AAC/C,EAAE,QAAQ,EAAEA,8BAAsB,CAAC,QAAQ;AAC3C,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,GAAGC,kBAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,iBAAiB,GAAG;AACjC,EAAE,CAACC,wBAAkB,GAAG,CAAC,KAAK,KAAKC,eAAQ,CAAC,KAAK,CAAC;AAClD,EAAE,CAACC,iBAAW,GAAG,CAAC,KAAK,KAAKD,eAAQ,CAAC,KAAK,CAAC;AAC3C,EAAE,CAACE,kBAAY,GAAG,CAAC,KAAK,KAAKF,eAAQ,CAAC,KAAK,CAAC;AAC5C,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC1C,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB,EAAE,MAAM,EAAE,CAAC,IAAI,KAAKG,eAAQ,CAAC,IAAI,CAAC;AAClC;;;;;"}