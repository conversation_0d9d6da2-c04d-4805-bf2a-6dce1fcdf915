# 汉字纹身AI平台本地启动脚本（无Docker）
# Chinese Character Tattoo AI Platform Local Startup Script (No Docker)

Write-Host "=== 汉字纹身AI平台本地启动 ===" -ForegroundColor Green

# 检查是否在项目根目录
if (-not (Test-Path "开发计划书.md")) {
    Write-Host "❌ 请在项目根目录运行此脚本" -ForegroundColor Red
    exit 1
}

# 检查Python环境
Write-Host "检查Python环境..." -ForegroundColor Yellow
if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 未找到Python，请先安装Python 3.11+" -ForegroundColor Red
    exit 1
}

# 检查PostgreSQL连接
Write-Host "检查PostgreSQL连接..." -ForegroundColor Yellow
try {
    $env:PGPASSWORD = "password123"
    psql -U postgres -d chinese_tattoo_ai -c "SELECT 1;" | Out-Null
    Write-Host "✓ PostgreSQL连接正常" -ForegroundColor Green
} catch {
    Write-Host "❌ PostgreSQL连接失败，请确保：" -ForegroundColor Red
    Write-Host "  1. PostgreSQL服务已启动" -ForegroundColor Yellow
    Write-Host "  2. 数据库 'chinese_tattoo_ai' 已创建" -ForegroundColor Yellow
    Write-Host "  3. 用户名密码正确" -ForegroundColor Yellow
    exit 1
}

# 设置后端环境
Write-Host "设置后端环境..." -ForegroundColor Yellow
Set-Location backend

# 创建虚拟环境（如果不存在）
if (-not (Test-Path "venv")) {
    Write-Host "创建Python虚拟环境..." -ForegroundColor Yellow
    python -m venv venv
}

# 激活虚拟环境
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
& .\venv\Scripts\Activate.ps1

# 安装依赖
Write-Host "安装后端依赖..." -ForegroundColor Yellow
pip install -r requirements.txt
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 后端依赖安装失败" -ForegroundColor Red
    exit 1
}

# 创建环境变量文件
if (-not (Test-Path ".env")) {
    Write-Host "创建环境变量文件..." -ForegroundColor Yellow
    Copy-Item ".env.example" ".env"
    Write-Host "✓ 已创建 .env 文件，请根据需要修改配置" -ForegroundColor Green
}

# 初始化数据库
Write-Host "初始化数据库..." -ForegroundColor Yellow
python init_db.py
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 数据库初始化失败" -ForegroundColor Red
    exit 1
}

# 启动后端服务
Write-Host "启动后端服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PWD'; .\venv\Scripts\Activate.ps1; uvicorn main:app --reload --host 0.0.0.0 --port 8000"

Set-Location ..

# 检查Node.js环境
Write-Host "检查Node.js环境..." -ForegroundColor Yellow
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 未找到Node.js，请先安装Node.js 18+" -ForegroundColor Red
    exit 1
}

# 设置前端环境
Write-Host "设置前端环境..." -ForegroundColor Yellow
Set-Location frontend

# 安装前端依赖
if (-not (Test-Path "node_modules")) {
    Write-Host "安装前端依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 前端依赖安装失败" -ForegroundColor Red
        exit 1
    }
}

# 创建前端环境变量文件
if (-not (Test-Path ".env.local")) {
    Write-Host "创建前端环境变量文件..." -ForegroundColor Yellow
    @"
VITE_API_BASE_URL=http://localhost:8000/api/v1
"@ | Out-File -FilePath ".env.local" -Encoding UTF8
}

# 启动前端服务
Write-Host "启动前端服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PWD'; npm run dev"

Set-Location ..

Write-Host ""
Write-Host "🎉 本地开发环境启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "服务地址：" -ForegroundColor Cyan
Write-Host "  前端应用: http://localhost:5173" -ForegroundColor White
Write-Host "  后端API: http://localhost:8000" -ForegroundColor White
Write-Host "  API文档: http://localhost:8000/docs" -ForegroundColor White
Write-Host ""
Write-Host "数据库连接：" -ForegroundColor Cyan
Write-Host "  PostgreSQL: localhost:5432/chinese_tattoo_ai" -ForegroundColor White
Write-Host ""
Write-Host "测试账户：" -ForegroundColor Cyan
Write-Host "  可以注册新账户或使用API文档测试" -ForegroundColor White
Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Yellow
Read-Host
