"""
AI服务 - 使用DeepSeek
AI Service using DeepSeek for character recommendations
"""

import json
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from app.core.config import settings
from app.models.character import ChineseCharacter, ChineseIdiom
from app.schemas.character import RecommendationResponse, CharacterRecommendation, IdiomRecommendation
from app.services.text_ai_service import TextAIService


class AIService:
    """AI服务类"""

    def __init__(self):
        self.text_ai_service = TextAIService()

    async def get_recommendations(
        self,
        keywords: List[str],
        description: Optional[str] = None,
        recommendation_type: str = "both",
        max_results: int = 5,
        db: Session = None,
    ) -> RecommendationResponse:
        """
        获取AI推荐的汉字和成语
        """

        # 构建用户描述
        user_description = self._build_user_description(keywords, description)

        try:
            # 调用文本AI API
            ai_data = await self.text_ai_service.recommend_characters(user_description, max_results)

            # 验证和丰富推荐结果
            recommendations = self._enrich_recommendations(ai_data)

            return recommendations

        except Exception as e:
            print(f"AI推荐失败: {e}")
            # 如果AI服务失败，返回基于数据库的推荐
            return self._fallback_recommendations(keywords, recommendation_type, max_results, db)

    def _build_user_description(self, keywords: List[str], description: Optional[str]) -> str:
        """构建用户描述"""

        parts = []

        if keywords:
            keywords_str = ", ".join(keywords)
            parts.append(f"关键词：{keywords_str}")

        if description:
            parts.append(f"详细描述：{description}")

        if not parts:
            return "请推荐一些适合纹身的汉字和成语"

        return " | ".join(parts)

    def _enrich_recommendations(self, ai_data: Dict[str, Any]) -> RecommendationResponse:
        """验证和丰富推荐结果"""

        characters = []
        idioms = []

        # 处理汉字推荐
        if "characters" in ai_data:
            for char_data in ai_data["characters"]:
                try:
                    character_rec = CharacterRecommendation(
                        character=char_data.get("character", ""),
                        pinyin=char_data.get("pinyin", ""),
                        meaning_en=char_data.get("meaning_en", ""),
                        meaning_zh=char_data.get("meaning_zh", ""),
                        cultural_context=char_data.get("cultural_context"),
                        score=float(char_data.get("score", 0.5)),
                        explanation=char_data.get("explanation", ""),
                    )
                    characters.append(character_rec)
                except Exception as e:
                    print(f"处理汉字推荐失败: {e}")
                    continue

        # 处理成语推荐
        if "idioms" in ai_data:
            for idiom_data in ai_data["idioms"]:
                try:
                    idiom_rec = IdiomRecommendation(
                        idiom=idiom_data.get("idiom", ""),
                        pinyin=idiom_data.get("pinyin", ""),
                        meaning_en=idiom_data.get("meaning_en", ""),
                        meaning_zh=idiom_data.get("meaning_zh", ""),
                        origin_story=idiom_data.get("origin_story"),
                        score=float(idiom_data.get("score", 0.5)),
                        explanation=idiom_data.get("explanation", ""),
                    )
                    idioms.append(idiom_rec)
                except Exception as e:
                    print(f"处理成语推荐失败: {e}")
                    continue

        ai_explanation = ai_data.get("ai_explanation", "基于您的需求，我为您推荐了以下汉字和成语。")

        return RecommendationResponse(characters=characters, idioms=idioms, ai_explanation=ai_explanation)

    def _fallback_recommendations(
        self, keywords: List[str], recommendation_type: str, max_results: int, db: Session
    ) -> RecommendationResponse:
        """备用推荐方案（基于数据库）"""

        characters = []
        idioms = []

        if not db:
            # 如果没有数据库连接，返回硬编码的推荐
            return self._get_hardcoded_recommendations()

        # 简单的关键词匹配
        keyword_str = " ".join(keywords).lower() if keywords else ""

        if recommendation_type in ["character", "both"]:
            try:
                db_characters = (
                    db.query(ChineseCharacter)
                    .filter(ChineseCharacter.is_active == True)
                    .order_by(ChineseCharacter.tattoo_popularity.desc())
                    .limit(max_results)
                    .all()
                )

                for char in db_characters:
                    character_rec = CharacterRecommendation(
                        character=char.char_text,
                        pinyin=char.pinyin,
                        meaning_en=char.meaning_en,
                        meaning_zh=char.meaning_zh,
                        cultural_context=char.cultural_context,
                        score=0.8,
                        explanation=f"基于数据库推荐的汉字：{char.char_text}",
                    )
                    characters.append(character_rec)
            except Exception as e:
                print(f"数据库查询汉字失败: {e}")

        if recommendation_type in ["idiom", "both"]:
            try:
                db_idioms = (
                    db.query(ChineseIdiom)
                    .filter(ChineseIdiom.is_active == True)
                    .order_by(ChineseIdiom.tattoo_popularity.desc())
                    .limit(max_results)
                    .all()
                )

                for idiom in db_idioms:
                    idiom_rec = IdiomRecommendation(
                        idiom=idiom.idiom,
                        pinyin=idiom.pinyin,
                        meaning_en=idiom.meaning_en,
                        meaning_zh=idiom.meaning_zh,
                        origin_story=idiom.origin_story,
                        score=0.8,
                        explanation=f"基于数据库推荐的成语：{idiom.idiom}",
                    )
                    idioms.append(idiom_rec)
            except Exception as e:
                print(f"数据库查询成语失败: {e}")

        return RecommendationResponse(
            characters=characters, idioms=idioms, ai_explanation="AI服务暂时不可用，为您提供基于数据库的推荐结果。"
        )

    def _get_hardcoded_recommendations(self) -> RecommendationResponse:
        """硬编码的备用推荐"""

        characters = [
            CharacterRecommendation(
                character="智",
                pinyin="zhì",
                meaning_en="wisdom, intelligence",
                meaning_zh="智慧，聪明",
                cultural_context="智慧是人生最宝贵的财富，代表着理性思考和明智决策的能力。",
                score=9.0,
                explanation="智慧是人生最重要的品质之一，适合作为纹身设计。",
            ),
            CharacterRecommendation(
                character="勇",
                pinyin="yǒng",
                meaning_en="courage, bravery",
                meaning_zh="勇敢，勇气",
                cultural_context="勇气是面对困难和挑战时的坚定意志。",
                score=8.8,
                explanation="勇气代表着面对困难的坚定意志，是很好的纹身选择。",
            ),
            CharacterRecommendation(
                character="爱",
                pinyin="ài",
                meaning_en="love, affection",
                meaning_zh="爱情，喜爱",
                cultural_context="爱是人类最基本的情感，代表着关怀、温暖和奉献。",
                score=9.2,
                explanation="爱是最美好的情感，适合表达内心的温暖。",
            ),
        ]

        idioms = [
            IdiomRecommendation(
                idiom="自强不息",
                pinyin="zì qiáng bù xī",
                meaning_en="constantly strive to become stronger",
                meaning_zh="自己努力向上，永不懈怠",
                origin_story="出自《周易》，象征着永不放弃的精神品质。",
                score=9.5,
                explanation="这个成语体现了永不放弃、持续进步的精神品质。",
            ),
            IdiomRecommendation(
                idiom="厚德载物",
                pinyin="hòu dé zài wù",
                meaning_en="virtue carries all things",
                meaning_zh="以深厚的德泽育人利物",
                origin_story="出自《周易》，强调品德修养的重要性。",
                score=9.0,
                explanation="强调品德修养的重要性，德行深厚才能承载万物。",
            ),
        ]

        return RecommendationResponse(
            characters=characters,
            idioms=idioms,
            ai_explanation="系统为您提供了经典的汉字和成语推荐，这些都具有深刻的文化内涵。",
        )
