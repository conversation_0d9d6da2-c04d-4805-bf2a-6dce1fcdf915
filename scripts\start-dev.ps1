# 汉字纹身AI平台开发环境启动脚本
# Chinese Character Tattoo AI Platform Development Startup Script

Write-Host "=== 汉字纹身AI平台开发环境启动 ===" -ForegroundColor Green

# 检查是否在项目根目录
if (-not (Test-Path "开发计划书.md")) {
    Write-Host "❌ 请在项目根目录运行此脚本" -ForegroundColor Red
    exit 1
}

# 检查Docker是否运行
Write-Host "检查Docker状态..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✓ Docker运行正常" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker未运行，请先启动Docker Desktop" -ForegroundColor Red
    exit 1
}

# 启动数据库和Redis服务
Write-Host "启动数据库和Redis服务..." -ForegroundColor Yellow
Set-Location docker
docker-compose up -d postgres redis
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 数据库服务启动失败" -ForegroundColor Red
    exit 1
}
Set-Location ..

# 等待数据库启动
Write-Host "等待数据库启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# 检查Python环境
Write-Host "检查Python环境..." -ForegroundColor Yellow
if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 未找到Python，请先安装Python 3.11+" -ForegroundColor Red
    exit 1
}

# 安装后端依赖
Write-Host "安装后端依赖..." -ForegroundColor Yellow
Set-Location backend
if (-not (Test-Path "venv")) {
    Write-Host "创建Python虚拟环境..." -ForegroundColor Yellow
    python -m venv venv
}

# 激活虚拟环境
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
& .\venv\Scripts\Activate.ps1

# 安装依赖
pip install -r requirements.txt
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 后端依赖安装失败" -ForegroundColor Red
    exit 1
}

# 初始化数据库
Write-Host "初始化数据库..." -ForegroundColor Yellow
python init_db.py
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 数据库初始化失败" -ForegroundColor Red
    exit 1
}

# 启动后端服务
Write-Host "启动后端服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PWD'; .\venv\Scripts\Activate.ps1; uvicorn main:app --reload --host 0.0.0.0 --port 8000"

Set-Location ..

# 检查Node.js环境
Write-Host "检查Node.js环境..." -ForegroundColor Yellow
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 未找到Node.js，请先安装Node.js 18+" -ForegroundColor Red
    exit 1
}

# 安装前端依赖
Write-Host "安装前端依赖..." -ForegroundColor Yellow
Set-Location frontend
if (-not (Test-Path "node_modules")) {
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 前端依赖安装失败" -ForegroundColor Red
        exit 1
    }
}

# 启动前端服务
Write-Host "启动前端服务..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PWD'; npm run dev"

Set-Location ..

Write-Host ""
Write-Host "🎉 开发环境启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "服务地址：" -ForegroundColor Cyan
Write-Host "  前端应用: http://localhost:5173" -ForegroundColor White
Write-Host "  后端API: http://localhost:8000" -ForegroundColor White
Write-Host "  API文档: http://localhost:8000/docs" -ForegroundColor White
Write-Host ""
Write-Host "数据库连接：" -ForegroundColor Cyan
Write-Host "  PostgreSQL: localhost:5432" -ForegroundColor White
Write-Host "  Redis: localhost:6379" -ForegroundColor White
Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Yellow
Read-Host
