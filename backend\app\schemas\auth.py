"""
认证相关数据模式
Authentication Related Data Schemas
"""

from pydantic import BaseModel
from typing import Optional
from .user import UserResponse, UserCreate, UserLogin

class Token(BaseModel):
    """令牌响应模式"""
    access_token: str
    token_type: str
    user: UserResponse

class TokenData(BaseModel):
    """令牌数据模式"""
    username: Optional[str] = None

# 重新导出用户相关模式以便在认证模块中使用
__all__ = ["Token", "TokenData", "UserCreate", "UserLogin", "UserResponse"]
