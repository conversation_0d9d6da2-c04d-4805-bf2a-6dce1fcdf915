-- 汉字纹身AI平台数据库模式
-- Chinese Character Tattoo AI Platform Database Schema

-- 创建数据库（如果需要）
-- CREATE DATABASE chinese_tattoo_ai;

-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    full_name VARCHAR(200),
    
    -- 用户状态
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    is_premium BOOLEAN DEFAULT FALSE,
    
    -- 积分系统
    credits INTEGER DEFAULT 0,
    
    -- 个人资料
    avatar_url VARCHAR(500),
    bio TEXT,
    language_preference VARCHAR(10) DEFAULT 'en',
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE
);

-- 汉字表
CREATE TABLE chinese_characters (
    id SERIAL PRIMARY KEY,
    character VARCHAR(10) UNIQUE NOT NULL,
    pinyin VARCHAR(50) NOT NULL,
    
    -- 含义和解释
    meaning_en TEXT NOT NULL,
    meaning_zh TEXT NOT NULL,
    cultural_context TEXT,
    usage_examples JSONB,
    
    -- 分类标签
    category VARCHAR(50),
    tags JSONB,
    
    -- 纹身适用性
    tattoo_popularity INTEGER DEFAULT 0,
    recommended_positions JSONB,
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 成语表
CREATE TABLE chinese_idioms (
    id SERIAL PRIMARY KEY,
    idiom VARCHAR(50) UNIQUE NOT NULL,
    pinyin VARCHAR(200) NOT NULL,
    
    -- 含义和解释
    meaning_en TEXT NOT NULL,
    meaning_zh TEXT NOT NULL,
    origin_story TEXT,
    cultural_context TEXT,
    usage_examples JSONB,
    
    -- 分类标签
    category VARCHAR(50),
    tags JSONB,
    difficulty_level INTEGER DEFAULT 1,
    
    -- 纹身适用性
    tattoo_popularity INTEGER DEFAULT 0,
    recommended_positions JSONB,
    character_count INTEGER NOT NULL,
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 纹身风格表
CREATE TABLE tattoo_styles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    name_zh VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- 风格特征
    characteristics JSONB,
    suitable_positions JSONB,
    difficulty_level INTEGER DEFAULT 1,
    
    -- 示例和参考
    example_images JSONB,
    prompt_template TEXT,
    
    -- 状态
    is_active BOOLEAN DEFAULT TRUE,
    is_premium BOOLEAN DEFAULT FALSE,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 纹身请求表
CREATE TABLE tattoo_requests (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    
    -- 用户输入
    keywords JSONB NOT NULL,
    description TEXT,
    personal_meaning TEXT,
    
    -- AI推荐结果
    recommended_characters JSONB,
    recommended_idioms JSONB,
    ai_explanation TEXT,
    
    -- 用户选择
    selected_type VARCHAR(20), -- 'character' 或 'idiom'
    selected_content VARCHAR(50),
    
    -- 纹身设计偏好
    preferred_style VARCHAR(50),
    preferred_position VARCHAR(50),
    size_preference VARCHAR(20),
    
    -- 状态
    status VARCHAR(20) DEFAULT 'pending', -- pending, completed, cancelled
    is_paid BOOLEAN DEFAULT FALSE,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 纹身图片表
CREATE TABLE tattoo_images (
    id SERIAL PRIMARY KEY,
    request_id INTEGER REFERENCES tattoo_requests(id) ON DELETE CASCADE,
    
    -- 图片信息
    image_url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    watermarked_url VARCHAR(500),
    
    -- 生成参数
    style VARCHAR(50) NOT NULL,
    position VARCHAR(50) NOT NULL,
    content VARCHAR(100) NOT NULL,
    
    -- 图片属性
    width INTEGER,
    height INTEGER,
    file_size INTEGER,
    
    -- AI生成信息
    generation_prompt TEXT,
    generation_model VARCHAR(50),
    generation_time FLOAT,
    
    -- 状态和质量
    is_high_resolution BOOLEAN DEFAULT FALSE,
    is_watermarked BOOLEAN DEFAULT TRUE,
    quality_score FLOAT,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 订单表
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    
    -- 订单信息
    order_type VARCHAR(20) NOT NULL, -- 'credits', 'premium', 'high_res_image'
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- 支付信息
    payment_status VARCHAR(20) DEFAULT 'pending', -- pending, completed, failed, refunded
    payment_method VARCHAR(50),
    payment_id VARCHAR(100),
    
    -- 订单内容
    credits_purchased INTEGER DEFAULT 0,
    premium_months INTEGER DEFAULT 0,
    related_request_id INTEGER REFERENCES tattoo_requests(id),
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_characters_character ON chinese_characters(character);
CREATE INDEX idx_characters_category ON chinese_characters(category);
CREATE INDEX idx_idioms_idiom ON chinese_idioms(idiom);
CREATE INDEX idx_idioms_category ON chinese_idioms(category);
CREATE INDEX idx_tattoo_requests_user_id ON tattoo_requests(user_id);
CREATE INDEX idx_tattoo_requests_status ON tattoo_requests(status);
CREATE INDEX idx_tattoo_images_request_id ON tattoo_images(request_id);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(payment_status);
