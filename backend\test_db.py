#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接
"""

from sqlalchemy import create_engine, text

def test_connection():
    """测试MySQL连接"""
    try:
        print("正在测试MySQL连接...")
        
        engine = create_engine(
            "mysql+pymysql://root:123456@localhost:3306/chinese_tattoo_ai?charset=utf8mb4"
        )
        
        with engine.connect() as conn:
            result = conn.execute(text("SELECT COUNT(*) as count FROM chinese_characters"))
            count = result.fetchone()[0]
            print(f"✓ 数据库连接成功！汉字表中有 {count} 条记录")
            
            result = conn.execute(text("SELECT COUNT(*) as count FROM tattoo_styles"))
            count = result.fetchone()[0]
            print(f"✓ 纹身风格表中有 {count} 条记录")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

if __name__ == "__main__":
    test_connection()
