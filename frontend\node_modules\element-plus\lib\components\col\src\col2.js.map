{"version": 3, "file": "col2.js", "sources": ["../../../../../../packages/components/col/src/col.vue"], "sourcesContent": ["<template>\n  <component :is=\"tag\" :class=\"colKls\" :style=\"style\">\n    <slot />\n  </component>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject } from 'vue'\nimport { isNumber, isObject } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { rowContextKey } from '@element-plus/components/row'\nimport { colProps } from './col'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElCol',\n})\n\nconst props = defineProps(colProps)\n\nconst { gutter } = inject(rowContextKey, { gutter: computed(() => 0) })\nconst ns = useNamespace('col')\n\nconst style = computed(() => {\n  const styles: CSSProperties = {}\n  if (gutter.value) {\n    styles.paddingLeft = styles.paddingRight = `${gutter.value / 2}px`\n  }\n  return styles\n})\n\nconst colKls = computed(() => {\n  const classes: string[] = []\n  const pos = ['span', 'offset', 'pull', 'push'] as const\n\n  pos.forEach((prop) => {\n    const size = props[prop]\n    if (isNumber(size)) {\n      if (prop === 'span') classes.push(ns.b(`${props[prop]}`))\n      else if (size > 0) classes.push(ns.b(`${prop}-${props[prop]}`))\n    }\n  })\n\n  const sizes = ['xs', 'sm', 'md', 'lg', 'xl'] as const\n  sizes.forEach((size) => {\n    if (isNumber(props[size])) {\n      classes.push(ns.b(`${size}-${props[size]}`))\n    } else if (isObject(props[size])) {\n      Object.entries(props[size]).forEach(([prop, sizeProp]) => {\n        classes.push(\n          prop !== 'span'\n            ? ns.b(`${size}-${prop}-${sizeProp}`)\n            : ns.b(`${size}-${sizeProp}`)\n        )\n      })\n    }\n  })\n\n  // this is for the fix\n  if (gutter.value) {\n    classes.push(ns.is('guttered'))\n  }\n  return [ns.b(), classes]\n})\n</script>\n"], "names": ["inject", "rowContextKey", "computed", "useNamespace", "isNumber", "isObject"], "mappings": ";;;;;;;;;;;;uCAcc,CAAA;AAAA,EACZ,IAAM,EAAA,OAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAE,MAAO,EAAA,GAAIA,UAAO,CAAAC,uBAAA,EAAe,EAAE,MAAA,EAAQC,YAAS,CAAA,MAAM,CAAC,CAAA,EAAG,CAAA,CAAA;AACtE,IAAM,MAAA,EAAA,GAAKC,mBAAa,KAAK,CAAA,CAAA;AAE7B,IAAM,MAAA,KAAA,GAAQD,aAAS,MAAM;AAC3B,MAAA,MAAM,SAAwB,EAAC,CAAA;AAC/B,MAAA,IAAI,OAAO,KAAO,EAAA;AAChB,QAAA,MAAA,CAAO,cAAc,MAAO,CAAA,YAAA,GAAe,CAAG,EAAA,MAAA,CAAO,QAAQ,CAAC,CAAA,EAAA,CAAA,CAAA;AAAA,OAChE;AACA,MAAO,OAAA,MAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,MAAA,GAASA,aAAS,MAAM;AAC5B,MAAA,MAAM,UAAoB,EAAC,CAAA;AAC3B,MAAA,MAAM,GAAM,GAAA,CAAC,MAAQ,EAAA,QAAA,EAAU,QAAQ,MAAM,CAAA,CAAA;AAE7C,MAAI,GAAA,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AACpB,QAAM,MAAA,IAAA,GAAO,MAAM,IAAI,CAAA,CAAA;AACvB,QAAI,IAAAE,cAAA,CAAS,IAAI,CAAG,EAAA;AAClB,UAAI,IAAA,IAAA,KAAS,MAAQ;AAAmC,YAAA,OAC/C,CAAO,IAAA,CAAA,EAAA,CAAG,CAAQ,CAAA,CAAA,EAAA,KAAA,CAAA,IAAQ,CAAA,CAAA,CAAA,CAAE,CAAG,CAAA;AAAsB,eAChE,IAAA,IAAA,GAAA,CAAA;AAAA,YACD,OAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,EAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAED,SAAA;AACA,OAAM,CAAA,CAAA;AACJ,MAAA,MAAI,KAAS,GAAA,CAAA,IAAA,EAAM,IAAI,EAAI,IAAA,EAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AACzB,MAAQ,KAAA,CAAA,OAAA,CAAA,CAAA,IAAQ,KAAK;AAAsB,QAC7C,IAAWA,cAAA,CAAA,KAAA,CAAA,IAAe,CAAA,CAAA,EAAA;AACxB,UAAO,OAAA,CAAA,IAAA,CAAA,EAAQ,CAAM,CAAA,CAAA,CAAA,EAAA,IAAI,CAAC,CAAA,YAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAC1C,SAAQ,MAAA,IAAAC,eAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,EAAA;AAAA,UAAA,cAEF,CAAA,KAAA,CAAA,IAAG,EAAE,CAAG,UAAQ,IAAI,EAAA,QAAY,CAAA;AACN,YAChC,OAAA,CAAA,IAAA,CAAA,IAAA,KAAA,MAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,WACD,CAAA,CAAA;AAAA,SACH;AAAA,OACD,CAAA,CAAA;AAGD,MAAA,IAAI,OAAO,KAAO,EAAA;AAChB,QAAA,OAAA,CAAQ,IAAK,CAAA,EAAA,CAAG,EAAG,CAAA,UAAU,CAAC,CAAA,CAAA;AAAA,OAChC;AACA,MAAA,OAAO,CAAC,EAAA,CAAG,CAAE,EAAA,EAAG,OAAO,CAAA,CAAA;AAAA,KACxB,CAAA,CAAA;;;;;;;;;;;;;;;;;;"}