{"version": 3, "file": "use-columns.mjs", "sources": ["../../../../../../../packages/components/table-v2/src/composables/use-columns.ts"], "sourcesContent": ["import { computed, unref } from 'vue'\nimport { isObject } from '@element-plus/utils'\nimport { SortOrder, oppositeOrderMap } from '../constants'\nimport { placeholderSign } from '../private'\nimport { calcColumnStyle } from './utils'\n\nimport type { CSSProperties, Ref } from 'vue'\nimport type { TableV2Props } from '../table'\nimport type { AnyColumns, Column, KeyType } from '../types'\n\nfunction useColumns(\n  props: TableV2Props,\n  columns: Ref<AnyColumns>,\n  fixed: Ref<boolean>\n) {\n  const _columns = computed(() =>\n    unref(columns).map((column, index) => ({\n      ...column,\n      key: column.key ?? column.dataKey ?? index,\n    }))\n  )\n\n  const visibleColumns = computed(() => {\n    return unref(_columns).filter((column) => !column.hidden)\n  })\n\n  const fixedColumnsOnLeft = computed(() =>\n    unref(visibleColumns).filter(\n      (column) => column.fixed === 'left' || column.fixed === true\n    )\n  )\n\n  const fixedColumnsOnRight = computed(() =>\n    unref(visibleColumns).filter((column) => column.fixed === 'right')\n  )\n\n  const normalColumns = computed(() =>\n    unref(visibleColumns).filter((column) => !column.fixed)\n  )\n\n  const mainColumns = computed(() => {\n    const ret: AnyColumns = []\n\n    unref(fixedColumnsOnLeft).forEach((column) => {\n      ret.push({\n        ...column,\n        placeholderSign,\n      })\n    })\n\n    unref(normalColumns).forEach((column) => {\n      ret.push(column)\n    })\n\n    unref(fixedColumnsOnRight).forEach((column) => {\n      ret.push({\n        ...column,\n        placeholderSign,\n      })\n    })\n\n    return ret\n  })\n\n  const hasFixedColumns = computed(() => {\n    return unref(fixedColumnsOnLeft).length || unref(fixedColumnsOnRight).length\n  })\n\n  const columnsStyles = computed(() => {\n    return unref(_columns).reduce<Record<KeyType, CSSProperties>>(\n      (style, column) => {\n        style[column.key] = calcColumnStyle(column, unref(fixed), props.fixed)\n        return style\n      },\n      {}\n    )\n  })\n\n  const columnsTotalWidth = computed(() => {\n    return unref(visibleColumns).reduce(\n      (width, column) => width + column.width,\n      0\n    )\n  })\n\n  const getColumn = (key: KeyType) => {\n    return unref(_columns).find((column) => column.key === key)\n  }\n\n  const getColumnStyle = (key: KeyType) => {\n    return unref(columnsStyles)[key]\n  }\n\n  const updateColumnWidth = (column: Column<any>, width: number) => {\n    column.width = width\n  }\n\n  function onColumnSorted(e: MouseEvent) {\n    const { key } = (e.currentTarget as HTMLElement).dataset\n    if (!key) return\n    const { sortState, sortBy } = props\n\n    let order = SortOrder.ASC\n\n    if (isObject(sortState)) {\n      order = oppositeOrderMap[sortState[key]]\n    } else {\n      order = oppositeOrderMap[sortBy.order]\n    }\n\n    props.onColumnSort?.({ column: getColumn(key)!, key, order })\n  }\n\n  return {\n    columns: _columns,\n    columnsStyles,\n    columnsTotalWidth,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    hasFixedColumns,\n    mainColumns,\n    normalColumns,\n    visibleColumns,\n\n    getColumn,\n    getColumnStyle,\n    updateColumnWidth,\n    onColumnSorted,\n  }\n}\n\nexport { useColumns }\nexport type UseColumnsReturn = ReturnType<typeof useColumns>\n"], "names": [], "mappings": ";;;;;;AAKA,SAAS,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE;AAC3C,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK;AACxE,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,OAAO;AACX,MAAM,GAAG,MAAM;AACf,MAAM,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,GAAG,KAAK,IAAI,GAAG,EAAE,GAAG,MAAM,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,KAAK;AACtF,KAAK,CAAC;AACN,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM;AACxC,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9D,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC;AACxI,EAAE,MAAM,mBAAmB,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC;AACjH,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAChG,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM;AACrC,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAClD,MAAM,GAAG,CAAC,IAAI,CAAC;AACf,QAAQ,GAAG,MAAM;AACjB,QAAQ,eAAe;AACvB,OAAO,CAAC,CAAC;AACT,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAC7C,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvB,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACnD,MAAM,GAAG,CAAC,IAAI,CAAC;AACf,QAAQ,GAAG,MAAM;AACjB,QAAQ,eAAe;AACvB,OAAO,CAAC,CAAC;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,GAAG,CAAC;AACf,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACzC,IAAI,OAAO,KAAK,CAAC,kBAAkB,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC;AACjF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM;AACvC,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,KAAK;AACrD,MAAM,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAC7E,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM;AAC3C,IAAI,OAAO,KAAK,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,KAAK,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACpF,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK;AAC7B,IAAI,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AAChE,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,GAAG,KAAK;AAClC,IAAI,OAAO,KAAK,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;AACrC,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,MAAM,EAAE,KAAK,KAAK;AAC/C,IAAI,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AACzB,GAAG,CAAC;AACJ,EAAE,SAAS,cAAc,CAAC,CAAC,EAAE;AAC7B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC;AAC5C,IAAI,IAAI,CAAC,GAAG;AACZ,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AACxC,IAAI,IAAI,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC;AAC9B,IAAI,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;AAC7B,MAAM,KAAK,GAAG,gBAAgB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/C,KAAK,MAAM;AACX,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;AACxG,GAAG;AACH,EAAE,OAAO;AACT,IAAI,OAAO,EAAE,QAAQ;AACrB,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,mBAAmB;AACvB,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,SAAS;AACb,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,cAAc;AAClB,GAAG,CAAC;AACJ;;;;"}