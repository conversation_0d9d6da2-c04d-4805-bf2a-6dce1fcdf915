{"version": 3, "file": "image.js", "sources": ["../../../../../../packages/components/image/src/image.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isNumber,\n  mutable,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type Image from './image.vue'\n\nexport const imageProps = buildProps({\n  /**\n   * @description when enabling preview, use this flag to control whether clicking on backdrop can exit preview mode.\n   */\n  hideOnClickModal: Boolean,\n  /**\n   * @description image source, same as native.\n   */\n  src: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description indicate how the image should be resized to fit its container, same as [object-fit](https://developer.mozilla.org/en-US/docs/Web/CSS/object-fit).\n   */\n  fit: {\n    type: String,\n    values: ['', 'contain', 'cover', 'fill', 'none', 'scale-down'],\n    default: '',\n  },\n  /**\n   * @description Indicates how the browser should load the image, same as [native](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#attr-loading)\n   */\n  loading: {\n    type: String,\n    values: ['eager', 'lazy'],\n  },\n  /**\n   * @description whether to use lazy load.\n   */\n  lazy: Boolean,\n  /**\n   * @description the container to add scroll listener when using lazy load.\n   */\n  scrollContainer: {\n    type: definePropType<string | HTMLElement | undefined>([String, Object]),\n  },\n  /**\n   * @description allow big image preview.\n   */\n  previewSrcList: {\n    type: definePropType<string[]>(Array),\n    default: () => mutable([] as const),\n  },\n  /**\n   * @description whether to append image-viewer to body. A nested parent element attribute transform should have this attribute set to `true`.\n   */\n  previewTeleported: Boolean,\n  /**\n   * @description set image preview z-index.\n   */\n  zIndex: {\n    type: Number,\n  },\n  /**\n   * @description initial preview image index, less than the length of `url-list`.\n   */\n  initialIndex: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description whether the viewer preview is infinite.\n   */\n  infinite: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether the image-viewer can be closed by pressing ESC.\n   */\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description the zoom rate of the image viewer zoom event\n   */\n  zoomRate: {\n    type: Number,\n    default: 1.2,\n  },\n  /**\n   * @description the min scale of the image viewer zoom event.\n   */\n  minScale: {\n    type: Number,\n    default: 0.2,\n  },\n  /**\n   * @description the max scale of the image viewer zoom event.\n   */\n  maxScale: {\n    type: Number,\n    default: 7,\n  },\n  /**\n   * @description show preview image progress content.\n   */\n  showProgress: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description set HTML attribute: crossorigin.\n   */\n  crossorigin: {\n    type: definePropType<'anonymous' | 'use-credentials' | ''>(String),\n  },\n} as const)\nexport type ImageProps = ExtractPropTypes<typeof imageProps>\n\nexport const imageEmits = {\n  load: (evt: Event) => evt instanceof Event,\n  error: (evt: Event) => evt instanceof Event,\n  switch: (val: number) => isNumber(val),\n  close: () => true,\n  show: () => true,\n}\nexport type ImageEmits = typeof imageEmits\nexport type ImageInstance = InstanceType<typeof Image> & unknown\n"], "names": ["buildProps", "definePropType", "mutable", "isNumber"], "mappings": ";;;;;;;;AAMY,MAAC,UAAU,GAAGA,kBAAU,CAAC;AACrC,EAAE,gBAAgB,EAAE,OAAO;AAC3B,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,CAAC;AAClE,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,iBAAiB,EAAE,OAAO;AAC5B,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAED,sBAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,CAAC,EAAE;AACS,MAAC,UAAU,GAAG;AAC1B,EAAE,IAAI,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,KAAK;AACrC,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,KAAK;AACtC,EAAE,MAAM,EAAE,CAAC,GAAG,KAAKE,cAAQ,CAAC,GAAG,CAAC;AAChC,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB,EAAE,IAAI,EAAE,MAAM,IAAI;AAClB;;;;;"}