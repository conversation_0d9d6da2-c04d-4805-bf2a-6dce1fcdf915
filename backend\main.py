"""
汉字纹身AI平台 - 主应用入口
Chinese Character Tattoo AI Platform - Main Application Entry
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
from app.core.config import settings
from app.api.v1.api import api_router

# 创建FastAPI应用实例
app = FastAPI(
    title="汉字纹身AI平台 API",
    description="Chinese Character Tattoo AI Platform API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置静态文件服务
uploads_dir = os.path.join(os.path.dirname(__file__), "uploads")
if os.path.exists(uploads_dir):
    app.mount("/uploads", StaticFiles(directory=uploads_dir), name="uploads")

# 注册API路由
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """根路径健康检查"""
    return JSONResponse(content={"message": "汉字纹身AI平台 API", "version": "1.0.0", "status": "running"})


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return JSONResponse(content={"status": "healthy", "service": "chinese-tattoo-ai-api"})


if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True, log_level="info")
