"""
纹身相关API端点
Tattoo Related API Endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional

from app.db.database import get_db
from app.models.user import User
from app.models.tattoo import TattooRequest, TattooImage, TattooStyle
from app.schemas.tattoo import (
    TattooRequestCreate,
    TattooRequestResponse,
    TattooImageResponse,
    TattooGenerationRequest,
    TattooStyleResponse,
)
from app.services.auth_service import AuthService
from app.services.tattoo_service import TattooService

router = APIRouter()


@router.post("/requests", response_model=TattooRequestResponse, summary="创建纹身请求")
async def create_tattoo_request(
    request_data: TattooRequestCreate,
    current_user: User = Depends(AuthService.get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    创建新的纹身设计请求

    - **keywords**: 关键词列表
    - **description**: 详细描述
    - **selected_type**: 选择的类型（character 或 idiom）
    - **selected_content**: 选择的汉字或成语
    - **preferred_style**: 偏好的风格
    - **preferred_position**: 偏好的身体部位
    """

    tattoo_service = TattooService(db)

    try:
        tattoo_request = tattoo_service.create_request(user_id=current_user.id, request_data=request_data)

        # 确保数据库会话提交
        db.commit()

        return tattoo_request

    except Exception as e:
        # 回滚事务
        db.rollback()
        print(f"创建纹身请求失败: {str(e)}")
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"创建纹身请求失败: {str(e)}")


@router.get("/requests", response_model=List[TattooRequestResponse], summary="获取用户纹身请求历史")
async def get_user_tattoo_requests(
    skip: int = 0,
    limit: int = 20,
    current_user: User = Depends(AuthService.get_current_active_user),
    db: Session = Depends(get_db),
):
    """获取当前用户的纹身请求历史"""

    requests = (
        db.query(TattooRequest)
        .filter(TattooRequest.user_id == current_user.id)
        .order_by(TattooRequest.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )

    return requests


@router.get("/requests/{request_id}", response_model=TattooRequestResponse, summary="获取纹身请求详情")
async def get_tattoo_request_detail(
    request_id: int, current_user: User = Depends(AuthService.get_current_active_user), db: Session = Depends(get_db)
):
    """获取指定纹身请求的详细信息"""

    request = (
        db.query(TattooRequest).filter(TattooRequest.id == request_id, TattooRequest.user_id == current_user.id).first()
    )

    if not request:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="纹身请求不存在")

    return request


@router.post("/generate", response_model=TattooImageResponse, summary="生成纹身预览图")
async def generate_tattoo_image(
    generation_request: TattooGenerationRequest,
    current_user: User = Depends(AuthService.get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    生成纹身预览图

    - **request_id**: 纹身请求ID
    - **style**: 风格
    - **position**: 身体部位
    - **content**: 汉字或成语内容
    - **high_resolution**: 是否生成高分辨率图片
    """

    # 验证纹身请求是否属于当前用户
    tattoo_request = (
        db.query(TattooRequest)
        .filter(TattooRequest.id == generation_request.request_id, TattooRequest.user_id == current_user.id)
        .first()
    )

    if not tattoo_request:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="纹身请求不存在")

    # 检查积分（高分辨率图片需要更多积分）
    required_credits = 5 if generation_request.high_resolution else 2

    if not current_user.is_premium and current_user.credits < required_credits:
        raise HTTPException(
            status_code=status.HTTP_402_PAYMENT_REQUIRED, detail=f"积分不足，需要 {required_credits} 积分"
        )

    tattoo_service = TattooService(db)

    try:
        # 生成纹身图片
        tattoo_image = await tattoo_service.generate_image(
            request_id=generation_request.request_id,
            style=generation_request.style,
            position=generation_request.position,
            content=generation_request.content,
            high_resolution=generation_request.high_resolution,
        )

        # 扣除积分（免费用户）
        if not current_user.is_premium:
            current_user.credits -= required_credits
            db.commit()

        return tattoo_image

    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"图片生成失败: {str(e)}")


@router.get("/images/{image_id}", response_model=TattooImageResponse, summary="获取纹身图片详情")
async def get_tattoo_image_detail(
    image_id: int, current_user: User = Depends(AuthService.get_current_active_user), db: Session = Depends(get_db)
):
    """获取纹身图片的详细信息"""

    # 通过关联查询验证图片是否属于当前用户
    image = (
        db.query(TattooImage)
        .join(TattooRequest)
        .filter(TattooImage.id == image_id, TattooRequest.user_id == current_user.id)
        .first()
    )

    if not image:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="纹身图片不存在")

    return image


@router.post("/images/{image_id}/unlock", summary="解锁高清图片")
async def unlock_high_resolution_image(
    image_id: int, current_user: User = Depends(AuthService.get_current_active_user), db: Session = Depends(get_db)
):
    """解锁高分辨率无水印图片"""

    # 验证图片是否属于当前用户
    image = (
        db.query(TattooImage)
        .join(TattooRequest)
        .filter(TattooImage.id == image_id, TattooRequest.user_id == current_user.id)
        .first()
    )

    if not image:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="纹身图片不存在")

    if image.is_high_resolution and not image.is_watermarked:
        return {"message": "图片已经是高清无水印版本"}

    # 检查积分
    required_credits = 10

    if not current_user.is_premium and current_user.credits < required_credits:
        raise HTTPException(
            status_code=status.HTTP_402_PAYMENT_REQUIRED, detail=f"积分不足，需要 {required_credits} 积分解锁高清图片"
        )

    tattoo_service = TattooService(db)

    try:
        # 生成高清无水印版本
        updated_image = await tattoo_service.unlock_high_resolution(image_id)

        # 扣除积分（免费用户）
        if not current_user.is_premium:
            current_user.credits -= required_credits
            db.commit()

        return {
            "message": "高清图片解锁成功",
            "image_url": updated_image.image_url,
            "credits_used": required_credits,
            "remaining_credits": current_user.credits,
        }

    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"解锁高清图片失败: {str(e)}")


@router.get("/styles", response_model=List[TattooStyleResponse], summary="获取可用的纹身风格")
async def get_tattoo_styles(db: Session = Depends(get_db)):
    """获取所有可用的纹身风格"""

    styles = db.query(TattooStyle).filter(TattooStyle.is_active == True).order_by(TattooStyle.name).all()

    return styles
