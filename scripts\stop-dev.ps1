# 汉字纹身AI平台开发环境停止脚本
# Chinese Character Tattoo AI Platform Development Stop Script

Write-Host "=== 停止汉字纹身AI平台开发环境 ===" -ForegroundColor Yellow

# 停止Docker服务
Write-Host "停止Docker服务..." -ForegroundColor Yellow
Set-Location docker
docker-compose down
Set-Location ..

# 停止可能运行的Python进程
Write-Host "停止后端服务..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -eq "python" -and $_.CommandLine -like "*uvicorn*"} | Stop-Process -Force -ErrorAction SilentlyContinue

# 停止可能运行的Node.js进程
Write-Host "停止前端服务..." -ForegroundColor Yellow
Get-Process | Where-Object {$_.ProcessName -eq "node" -and $_.CommandLine -like "*vite*"} | Stop-Process -Force -ErrorAction SilentlyContinue

Write-Host "✓ 开发环境已停止" -ForegroundColor Green
