<template>
  <footer class="app-footer">
    <div class="footer-container">
      <!-- 主要内容区域 -->
      <div class="footer-content">
        <!-- 品牌信息 -->
        <div class="footer-section brand-section">
          <div class="footer-brand">
            <div class="footer-logo">
              <span class="chinese-char">墨</span>
            </div>
            <div>
              <h3>墨痕智纹</h3>
              <p>Chinese Character Tattoo AI Platform</p>
            </div>
          </div>
          <p class="brand-description">
            为全球用户提供专业的汉字纹身设计建议，融合传统文化与现代AI技术，
            让每一个汉字都承载着深刻的文化内涵。
          </p>
          <div class="social-links">
            <el-button circle size="small" type="text">
              <el-icon><Platform /></el-icon>
            </el-button>
            <el-button circle size="small" type="text">
              <el-icon><ChatDotRound /></el-icon>
            </el-button>
            <el-button circle size="small" type="text">
              <el-icon><Message /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="footer-section">
          <h4>快速链接</h4>
          <ul class="footer-links">
            <li><router-link to="/">首页</router-link></li>
            <li><router-link to="/search">搜索汉字</router-link></li>
            <li><router-link to="/recommend">AI推荐</router-link></li>
            <li><router-link to="/about">关于我们</router-link></li>
          </ul>
        </div>

        <!-- 服务支持 -->
        <div class="footer-section">
          <h4>服务支持</h4>
          <ul class="footer-links">
            <li><a href="#help">帮助中心</a></li>
            <li><a href="#faq">常见问题</a></li>
            <li><a href="#contact">联系我们</a></li>
            <li><a href="#feedback">意见反馈</a></li>
          </ul>
        </div>

        <!-- 法律信息 -->
        <div class="footer-section">
          <h4>法律信息</h4>
          <ul class="footer-links">
            <li><a href="#privacy">隐私政策</a></li>
            <li><a href="#terms">服务条款</a></li>
            <li><a href="#copyright">版权声明</a></li>
            <li><a href="#disclaimer">免责声明</a></li>
          </ul>
        </div>
      </div>

      <!-- 底部版权信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p>&copy; 2024 墨痕智纹 (Chinese Character Tattoo AI). All rights reserved.</p>
          <p>本平台致力于传播中华文化，请理性对待纹身，尊重文化内涵。</p>
        </div>
        <div class="footer-meta">
          <span>Made with ❤️ for Chinese Culture</span>
          <span>|</span>
          <span>Powered by AI Technology</span>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { Platform, ChatDotRound, Message } from '@element-plus/icons-vue'
</script>

<style scoped>
.app-footer {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 48px 24px 24px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

/* 品牌区域 */
.brand-section {
  max-width: 350px;
}

.footer-brand {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.footer-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.chinese-char {
  font-size: 20px;
  font-weight: bold;
  color: #1a1a1a;
  font-family: 'SimSun', serif;
}

.footer-brand h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #ffffff;
  font-family: 'SimSun', serif;
}

.footer-brand p {
  font-size: 12px;
  margin: 0;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

.brand-description {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin-bottom: 20px;
  font-size: 14px;
}

.social-links {
  display: flex;
  gap: 8px;
}

.social-links .el-button {
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.social-links .el-button:hover {
  color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
}

/* 链接区域 */
.footer-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 20px 0;
  position: relative;
}

.footer-section h4::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #d4af37, #ffd700);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  display: inline-block;
}

.footer-links a:hover {
  color: #ffd700;
  transform: translateX(4px);
}

/* 底部版权区域 */
.footer-bottom {
  padding-top: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.copyright p {
  margin: 0 0 4px 0;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
}

.footer-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 32px;
  }
  
  .brand-section {
    grid-column: 1 / -1;
    max-width: none;
  }
}

@media (max-width: 768px) {
  .footer-container {
    padding: 32px 16px 16px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .footer-meta {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
