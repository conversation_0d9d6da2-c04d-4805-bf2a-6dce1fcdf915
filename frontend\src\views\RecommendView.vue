<template>
  <div class="recommend-page">
    <!-- 页面头部 -->
    <section class="recommend-header">
      <div class="container">
        <div class="header-content">
          <h1>AI纹身推荐</h1>
          <p>告诉我们您的想法，AI为您推荐最合适的汉字和成语，并生成纹身设计</p>
        </div>
      </div>
    </section>

    <!-- 输入区域 -->
    <section class="input-section">
      <div class="container">
        <el-card class="input-card">
          <div class="input-form">
            <el-input
              v-model="userInput"
              type="textarea"
              :rows="4"
              placeholder="请详细描述您希望纹身表达的含义、情感或故事背景...&#10;例如：我想要一个代表坚强和勇气的纹身，能够激励我在困难时不放弃"
              maxlength="500"
              show-word-limit
              class="user-input"
            />
            <div class="input-actions">
              <el-button
                type="primary"
                size="large"
                @click="getAIRecommendations"
                :loading="isLoading"
                :disabled="!userInput.trim()"
              >
                <el-icon><MagicStick /></el-icon>
                获取AI推荐
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </section>

    <!-- AI推荐结果 -->
    <section class="recommendations-section" v-if="aiRecommendations.length > 0">
      <div class="container">
        <div class="results-header">
          <h2>AI推荐结果</h2>
          <p>{{ aiExplanation }}</p>
        </div>

        <!-- 推荐的汉字/成语选择 -->
        <div class="recommendations-grid">
          <div
            v-for="(item, index) in aiRecommendations"
            :key="index"
            class="recommendation-item"
          >
            <div class="item-content">
              <div class="item-text">{{ item.content }}</div>
              <div class="item-info">
                <div class="pinyin">{{ item.pinyin }}</div>
                <div class="meaning">{{ item.meaning }}</div>
                <div class="explanation">{{ item.explanation }}</div>
              </div>
            </div>
            <div class="item-actions">
              <el-button
                type="primary"
                @click="generateTattooDesign(item)"
                :loading="item.generating"
                :disabled="item.generating"
              >
                <el-icon><Picture /></el-icon>
                生成纹身图
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 生成的纹身图片列表 -->
    <section class="tattoo-gallery" v-if="tattooImages.length > 0">
      <div class="container">
        <div class="gallery-header">
          <h2>生成的纹身设计</h2>
          <p>点击图片查看大图，满意的设计可以支付下载高清版</p>
        </div>

        <!-- 图片网格 -->
        <div class="images-grid">
          <div
            v-for="(image, index) in paginatedImages"
            :key="image.id"
            class="image-item"
            @click="previewImage(image)"
          >
            <div class="image-container">
              <img :src="image.thumbnail_url || image.image_url" :alt="image.content" />
              <div class="image-overlay">
                <div class="image-info">
                  <div class="content">{{ image.content }}</div>
                  <div class="style">{{ image.style }}</div>
                </div>
                <div class="image-actions">
                  <el-button size="small" @click.stop="previewImage(image)">
                    <el-icon><View /></el-icon>
                    预览
                  </el-button>
                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="purchaseHighRes(image)"
                    v-if="image.is_watermarked"
                  >
                    <el-icon><Download /></el-icon>
                    购买高清
                  </el-button>
                  <el-button
                    type="success"
                    size="small"
                    @click.stop="downloadImage(image)"
                    v-else
                  >
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container" v-if="tattooImages.length > pageSize">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="tattooImages.length"
            layout="prev, pager, next"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </section>

    <!-- 积分不足提示 -->
    <el-dialog
      v-model="showCreditDialog"
      title="积分不足"
      width="400px"
      center
    >
      <div class="credit-dialog-content">
        <el-icon size="48" color="#ffd700"><Coin /></el-icon>
        <p>您的积分不足，无法使用AI推荐功能</p>
        <p>当前积分：{{ userStore.user?.credits || 0 }}</p>
        <p>所需积分：1</p>
      </div>
      <template #footer>
        <el-button @click="showCreditDialog = false">取消</el-button>
        <el-button type="primary" @click="goToRecharge">充值积分</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElImageViewer } from 'element-plus'
import { MagicStick, Picture, View, Download, Coin } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/counter'
import { CharacterService } from '@/services/character'
import { TattooService } from '@/services/tattoo'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const userInput = ref('')
const isLoading = ref(false)
const aiRecommendations = ref<any[]>([])
const aiExplanation = ref('')
const tattooImages = ref<any[]>([])
const showCreditDialog = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const paginatedImages = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return tattooImages.value.slice(start, end)
})

onMounted(() => {
  // 检查用户登录状态
  if (!userStore.isAuthenticated) {
    ElMessage.warning('请先登录')
    router.push('/login')
  }
})

// 处理关键词变化
const handleKeywordsChange = (keywords: string[]) => {
  if (keywords.length > 5) {
    ElMessage.warning('最多只能选择5个关键词')
    recommendForm.keywords = keywords.slice(0, 5)
  }
}

// 获取AI推荐
const handleGetRecommendations = async () => {
  if (!recommendFormRef.value) return

  try {
    await recommendFormRef.value.validate()

    // 检查积分
    if (!userStore.user?.is_premium && (userStore.user?.credits || 0) < 1) {
      showCreditDialog.value = true
      return
    }

    isLoading.value = true

    const result = await CharacterService.getRecommendations(recommendForm)
    recommendations.value = result

    // 更新用户积分
    if (!userStore.user?.is_premium) {
      userStore.updateCredits((userStore.user?.credits || 0) - 1)
    }

    ElMessage.success('AI推荐获取成功！')

  } catch (error) {
    console.error('获取推荐失败:', error)
    ElMessage.error('获取推荐失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 选择汉字
const selectCharacter = (character: CharacterRecommendation) => {
  ElMessage.success(`您选择了汉字：${character.character}`)
}

// 选择成语
const selectIdiom = (idiom: IdiomRecommendation) => {
  ElMessage.success(`您选择了成语：${idiom.idiom}`)
}

// 创建纹身设计
const createTattooDesign = (item: CharacterRecommendation | IdiomRecommendation) => {
  const content = 'character' in item ? item.character : item.idiom
  const type = 'character' in item ? 'character' : 'idiom'
  
  router.push({
    path: '/tattoo-design',
    query: {
      content,
      type
    }
  })
}

// 清除推荐结果
const clearRecommendations = () => {
  recommendations.value = null
}

// 前往充值
const goToRecharge = () => {
  showCreditDialog.value = false
  router.push('/profile?tab=credits')
}
</script>

<style scoped>
.recommend-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 页面头部 */
.recommend-header {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-content {
  text-align: center;
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
  font-family: 'SimSun', serif;
}

.header-content p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
}

/* 推荐表单区域 */
.recommend-form-section {
  padding: 40px 0;
}

.recommend-card {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.card-header {
  text-align: center;
}

.card-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.card-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
}

.form-tip {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
}

/* 推荐结果区域 */
.recommendations-section {
  padding: 40px 0 80px;
}

.results-header {
  text-align: center;
  margin-bottom: 50px;
}

.results-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.results-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 215, 0, 0.1);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.recommendations-group {
  margin-bottom: 50px;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffd700;
  margin: 0 0 30px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(255, 215, 0, 0.3);
}

/* 汉字推荐网格 */
.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.character-recommendation {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.character-recommendation:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.character-main {
  font-size: 4rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  text-align: center;
  margin-bottom: 20px;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.character-info .pinyin {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 8px;
  font-style: italic;
}

.character-info .meaning-en {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-bottom: 6px;
  font-weight: 500;
}

.character-info .meaning-zh {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 12px;
}

.cultural-context {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.explanation {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 16px;
  font-style: italic;
}

.score {
  text-align: center;
  margin-bottom: 16px;
}

.action-buttons {
  text-align: center;
}

/* 成语推荐网格 */
.idioms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.idiom-recommendation {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.idiom-recommendation:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.idiom-main {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  text-align: center;
  margin-bottom: 20px;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.idiom-info .pinyin {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 8px;
  font-style: italic;
}

.idiom-info .meaning-en {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-bottom: 6px;
  font-weight: 500;
}

.idiom-info .meaning-zh {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 12px;
}

.origin-story {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

/* 重新推荐区域 */
.retry-section {
  text-align: center;
  margin-top: 40px;
}

/* 积分对话框 */
.credit-dialog-content {
  text-align: center;
  padding: 20px;
}

.credit-dialog-content p {
  margin: 12px 0;
  color: #666;
}

/* 表单样式覆盖 */
:deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

:deep(.el-card__header) {
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-card__body) {
  background: transparent;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-textarea .el-textarea__inner) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

:deep(.el-radio__label) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-slider__runway) {
  background: rgba(255, 255, 255, 0.2);
}

:deep(.el-slider__bar) {
  background: linear-gradient(90deg, #d4af37, #ffd700);
}

:deep(.el-slider__button) {
  border-color: #ffd700;
}

:deep(.el-rate__icon) {
  color: #ffd700;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content h1 {
    font-size: 2rem;
  }

  .characters-grid,
  .idioms-grid {
    grid-template-columns: 1fr;
  }

  .character-main {
    font-size: 3rem;
  }

  .idiom-main {
    font-size: 2rem;
  }

  .recommend-card {
    margin: 0 16px;
  }
}
</style>
