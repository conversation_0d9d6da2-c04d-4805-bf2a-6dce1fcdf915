<template>
  <div class="recommend-page">
    <!-- 页面头部 -->
    <section class="recommend-header">
      <div class="container">
        <div class="header-content">
          <h1>AI智能推荐</h1>
          <p>告诉我们您的想法，让AI为您推荐最合适的汉字和成语</p>
        </div>
      </div>
    </section>

    <!-- 推荐表单 -->
    <section class="recommend-form-section">
      <div class="container">
        <el-card class="recommend-card">
          <template #header>
            <div class="card-header">
              <h2>描述您的需求</h2>
              <p>请详细描述您希望纹身表达的含义和情感</p>
            </div>
          </template>

          <el-form
            ref="recommendFormRef"
            :model="recommendForm"
            :rules="recommendRules"
            label-width="120px"
            size="large"
          >
            <!-- 关键词输入 -->
            <el-form-item label="关键词" prop="keywords">
              <el-select
                v-model="recommendForm.keywords"
                multiple
                filterable
                allow-create
                placeholder="输入关键词，如：love, strength, wisdom..."
                style="width: 100%"
                @change="handleKeywordsChange"
              >
                <el-option
                  v-for="keyword in suggestedKeywords"
                  :key="keyword"
                  :label="keyword"
                  :value="keyword"
                />
              </el-select>
              <div class="form-tip">
                输入英文关键词，描述您想要表达的含义
              </div>
            </el-form-item>

            <!-- 详细描述 -->
            <el-form-item label="详细描述" prop="description">
              <el-input
                v-model="recommendForm.description"
                type="textarea"
                :rows="4"
                placeholder="请详细描述您希望纹身表达的含义、情感或故事背景..."
                maxlength="500"
                show-word-limit
              />
              <div class="form-tip">
                详细的描述有助于AI提供更准确的推荐
              </div>
            </el-form-item>

            <!-- 推荐类型 -->
            <el-form-item label="推荐类型" prop="type">
              <el-radio-group v-model="recommendForm.type">
                <el-radio label="both">汉字和成语</el-radio>
                <el-radio label="character">仅汉字</el-radio>
                <el-radio label="idiom">仅成语</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 结果数量 -->
            <el-form-item label="推荐数量">
              <el-slider
                v-model="recommendForm.max_results"
                :min="3"
                :max="10"
                :step="1"
                show-stops
                show-input
                style="width: 300px"
              />
            </el-form-item>

            <!-- 提交按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                @click="handleGetRecommendations"
                :loading="isLoading"
                style="width: 200px"
              >
                <el-icon><MagicStick /></el-icon>
                获取AI推荐
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </section>

    <!-- 推荐结果 -->
    <section class="recommendations-section" v-if="recommendations">
      <div class="container">
        <div class="results-header">
          <h2>AI推荐结果</h2>
          <p>{{ recommendations.ai_explanation }}</p>
        </div>

        <!-- 汉字推荐 -->
        <div v-if="recommendations.characters.length > 0" class="recommendations-group">
          <h3 class="group-title">
            <el-icon><Document /></el-icon>
            推荐汉字
          </h3>
          <div class="characters-grid">
            <div
              v-for="(character, index) in recommendations.characters"
              :key="index"
              class="character-recommendation"
              @click="selectCharacter(character)"
            >
              <div class="character-main">{{ character.character }}</div>
              <div class="character-info">
                <div class="pinyin">{{ character.pinyin }}</div>
                <div class="meaning-en">{{ character.meaning_en }}</div>
                <div class="meaning-zh">{{ character.meaning_zh }}</div>
                <div class="cultural-context" v-if="character.cultural_context">
                  {{ character.cultural_context }}
                </div>
                <div class="explanation">{{ character.explanation }}</div>
                <div class="score">
                  <el-rate
                    v-model="character.score"
                    disabled
                    show-score
                    text-color="#ffd700"
                    score-template="{value}分"
                  />
                </div>
              </div>
              <div class="action-buttons">
                <el-button type="primary" size="small" @click.stop="createTattooDesign(character)">
                  生成纹身设计
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 成语推荐 -->
        <div v-if="recommendations.idioms.length > 0" class="recommendations-group">
          <h3 class="group-title">
            <el-icon><Collection /></el-icon>
            推荐成语
          </h3>
          <div class="idioms-grid">
            <div
              v-for="(idiom, index) in recommendations.idioms"
              :key="index"
              class="idiom-recommendation"
              @click="selectIdiom(idiom)"
            >
              <div class="idiom-main">{{ idiom.idiom }}</div>
              <div class="idiom-info">
                <div class="pinyin">{{ idiom.pinyin }}</div>
                <div class="meaning-en">{{ idiom.meaning_en }}</div>
                <div class="meaning-zh">{{ idiom.meaning_zh }}</div>
                <div class="origin-story" v-if="idiom.origin_story">
                  <strong>典故：</strong>{{ idiom.origin_story }}
                </div>
                <div class="explanation">{{ idiom.explanation }}</div>
                <div class="score">
                  <el-rate
                    v-model="idiom.score"
                    disabled
                    show-score
                    text-color="#ffd700"
                    score-template="{value}分"
                  />
                </div>
              </div>
              <div class="action-buttons">
                <el-button type="primary" size="small" @click.stop="createTattooDesign(idiom)">
                  生成纹身设计
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 重新推荐按钮 -->
        <div class="retry-section">
          <el-button @click="clearRecommendations" size="large">
            重新推荐
          </el-button>
        </div>
      </div>
    </section>

    <!-- 积分不足提示 -->
    <el-dialog
      v-model="showCreditDialog"
      title="积分不足"
      width="400px"
      center
    >
      <div class="credit-dialog-content">
        <el-icon size="48" color="#ffd700"><Coin /></el-icon>
        <p>您的积分不足，无法使用AI推荐功能</p>
        <p>当前积分：{{ userStore.user?.credits || 0 }}</p>
        <p>所需积分：1</p>
      </div>
      <template #footer>
        <el-button @click="showCreditDialog = false">取消</el-button>
        <el-button type="primary" @click="goToRecharge">充值积分</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { MagicStick, Document, Collection, Coin } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/counter'
import { CharacterService } from '@/services/character'
import type { 
  RecommendationRequest, 
  RecommendationResponse,
  CharacterRecommendation,
  IdiomRecommendation
} from '@/types'

const router = useRouter()
const userStore = useUserStore()

const recommendFormRef = ref<FormInstance>()
const isLoading = ref(false)
const recommendations = ref<RecommendationResponse | null>(null)
const showCreditDialog = ref(false)

// 推荐表单
const recommendForm = reactive<RecommendationRequest>({
  keywords: [],
  description: '',
  type: 'both',
  max_results: 5
})

// 建议的关键词
const suggestedKeywords = ref([
  'love', 'strength', 'wisdom', 'peace', 'courage', 'harmony', 'fortune', 'family',
  'freedom', 'hope', 'faith', 'power', 'beauty', 'truth', 'honor', 'loyalty',
  'balance', 'growth', 'success', 'happiness', 'protection', 'journey'
])

// 表单验证规则
const recommendRules: FormRules = {
  keywords: [
    { required: true, message: '请至少输入一个关键词', trigger: 'change' }
  ]
}

onMounted(() => {
  // 检查用户登录状态
  if (!userStore.isAuthenticated) {
    ElMessage.warning('请先登录')
    router.push('/login')
  }
})

// 处理关键词变化
const handleKeywordsChange = (keywords: string[]) => {
  if (keywords.length > 5) {
    ElMessage.warning('最多只能选择5个关键词')
    recommendForm.keywords = keywords.slice(0, 5)
  }
}

// 获取AI推荐
const handleGetRecommendations = async () => {
  if (!recommendFormRef.value) return

  try {
    await recommendFormRef.value.validate()

    // 检查积分
    if (!userStore.user?.is_premium && (userStore.user?.credits || 0) < 1) {
      showCreditDialog.value = true
      return
    }

    isLoading.value = true

    const result = await CharacterService.getRecommendations(recommendForm)
    recommendations.value = result

    // 更新用户积分
    if (!userStore.user?.is_premium) {
      userStore.updateCredits((userStore.user?.credits || 0) - 1)
    }

    ElMessage.success('AI推荐获取成功！')

  } catch (error) {
    console.error('获取推荐失败:', error)
    ElMessage.error('获取推荐失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 选择汉字
const selectCharacter = (character: CharacterRecommendation) => {
  ElMessage.success(`您选择了汉字：${character.character}`)
}

// 选择成语
const selectIdiom = (idiom: IdiomRecommendation) => {
  ElMessage.success(`您选择了成语：${idiom.idiom}`)
}

// 创建纹身设计
const createTattooDesign = (item: CharacterRecommendation | IdiomRecommendation) => {
  const content = 'character' in item ? item.character : item.idiom
  const type = 'character' in item ? 'character' : 'idiom'
  
  router.push({
    path: '/tattoo-design',
    query: {
      content,
      type
    }
  })
}

// 清除推荐结果
const clearRecommendations = () => {
  recommendations.value = null
}

// 前往充值
const goToRecharge = () => {
  showCreditDialog.value = false
  router.push('/profile?tab=credits')
}
</script>

<style scoped>
.recommend-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 页面头部 */
.recommend-header {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-content {
  text-align: center;
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
  font-family: 'SimSun', serif;
}

.header-content p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
}

/* 推荐表单区域 */
.recommend-form-section {
  padding: 40px 0;
}

.recommend-card {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.card-header {
  text-align: center;
}

.card-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.card-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
}

.form-tip {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 4px;
}

/* 推荐结果区域 */
.recommendations-section {
  padding: 40px 0 80px;
}

.results-header {
  text-align: center;
  margin-bottom: 50px;
}

.results-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.results-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 215, 0, 0.1);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.recommendations-group {
  margin-bottom: 50px;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffd700;
  margin: 0 0 30px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(255, 215, 0, 0.3);
}

/* 汉字推荐网格 */
.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.character-recommendation {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.character-recommendation:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.character-main {
  font-size: 4rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  text-align: center;
  margin-bottom: 20px;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.character-info .pinyin {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 8px;
  font-style: italic;
}

.character-info .meaning-en {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-bottom: 6px;
  font-weight: 500;
}

.character-info .meaning-zh {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 12px;
}

.cultural-context {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.explanation {
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  margin-bottom: 16px;
  font-style: italic;
}

.score {
  text-align: center;
  margin-bottom: 16px;
}

.action-buttons {
  text-align: center;
}

/* 成语推荐网格 */
.idioms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.idiom-recommendation {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.idiom-recommendation:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.idiom-main {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  text-align: center;
  margin-bottom: 20px;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.idiom-info .pinyin {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 8px;
  font-style: italic;
}

.idiom-info .meaning-en {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-bottom: 6px;
  font-weight: 500;
}

.idiom-info .meaning-zh {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 12px;
}

.origin-story {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 12px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

/* 重新推荐区域 */
.retry-section {
  text-align: center;
  margin-top: 40px;
}

/* 积分对话框 */
.credit-dialog-content {
  text-align: center;
  padding: 20px;
}

.credit-dialog-content p {
  margin: 12px 0;
  color: #666;
}

/* 表单样式覆盖 */
:deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

:deep(.el-card__header) {
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-card__body) {
  background: transparent;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-textarea .el-textarea__inner) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

:deep(.el-radio__label) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-slider__runway) {
  background: rgba(255, 255, 255, 0.2);
}

:deep(.el-slider__bar) {
  background: linear-gradient(90deg, #d4af37, #ffd700);
}

:deep(.el-slider__button) {
  border-color: #ffd700;
}

:deep(.el-rate__icon) {
  color: #ffd700;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content h1 {
    font-size: 2rem;
  }

  .characters-grid,
  .idioms-grid {
    grid-template-columns: 1fr;
  }

  .character-main {
    font-size: 3rem;
  }

  .idiom-main {
    font-size: 2rem;
  }

  .recommend-card {
    margin: 0 16px;
  }
}
</style>
