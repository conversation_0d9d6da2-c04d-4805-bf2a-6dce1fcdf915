#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app.db.database import get_db
    from app.models.user import User
    from app.models.tattoo import TattooRequest
    from sqlalchemy.orm import Session
    
    print("🔍 测试数据库连接...")
    
    # 获取数据库会话
    db_gen = get_db()
    db: Session = next(db_gen)
    
    print("✅ 数据库连接成功")
    
    # 测试查询用户
    user_count = db.query(User).count()
    print(f"📊 用户数量: {user_count}")
    
    # 测试查询纹身请求
    request_count = db.query(TattooRequest).count()
    print(f"🎨 纹身请求数量: {request_count}")
    
    # 查找测试用户
    test_user = db.query(User).filter(User.username == "yangshu888").first()
    if test_user:
        print(f"👤 找到测试用户: {test_user.username} (ID: {test_user.id})")
    else:
        print("❌ 未找到测试用户")
    
    db.close()
    print("✅ 数据库测试完成")
    
except Exception as e:
    print(f"❌ 数据库连接失败: {e}")
    import traceback
    traceback.print_exc()
