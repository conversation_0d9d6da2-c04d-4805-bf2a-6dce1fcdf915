{"version": 3, "file": "menu-item2.js", "sources": ["../../../../../../packages/components/menu/src/menu-item.vue"], "sourcesContent": ["<template>\n  <li\n    :class=\"[\n      nsMenuItem.b(),\n      nsMenuItem.is('active', active),\n      nsMenuItem.is('disabled', disabled),\n    ]\"\n    role=\"menuitem\"\n    tabindex=\"-1\"\n    @click=\"handleClick\"\n  >\n    <el-tooltip\n      v-if=\"\n        parentMenu.type.name === 'ElMenu' &&\n        rootMenu.props.collapse &&\n        $slots.title\n      \"\n      :effect=\"rootMenu.props.popperEffect\"\n      placement=\"right\"\n      :fallback-placements=\"['left']\"\n      :persistent=\"rootMenu.props.persistent\"\n    >\n      <template #content>\n        <slot name=\"title\" />\n      </template>\n      <div :class=\"nsMenu.be('tooltip', 'trigger')\">\n        <slot />\n      </div>\n    </el-tooltip>\n    <template v-else>\n      <slot />\n      <slot name=\"title\" />\n    </template>\n  </li>\n</template>\n\n<script lang=\"ts\" setup>\n// @ts-nocheck\nimport {\n  computed,\n  getCurrentInstance,\n  inject,\n  onBeforeUnmount,\n  onMounted,\n  reactive,\n  toRef,\n} from 'vue'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport { throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport useMenu from './use-menu'\nimport { menuItemEmits, menuItemProps } from './menu-item'\n\nimport type { MenuItemRegistered, MenuProvider, SubMenuProvider } from './types'\n\nconst COMPONENT_NAME = 'ElMenuItem'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\nconst props = defineProps(menuItemProps)\nconst emit = defineEmits(menuItemEmits)\n\nconst instance = getCurrentInstance()!\nconst rootMenu = inject<MenuProvider>('rootMenu')\nconst nsMenu = useNamespace('menu')\nconst nsMenuItem = useNamespace('menu-item')\nif (!rootMenu) throwError(COMPONENT_NAME, 'can not inject root menu')\n\nconst { parentMenu, indexPath } = useMenu(instance, toRef(props, 'index'))\n\nconst subMenu = inject<SubMenuProvider>(`subMenu:${parentMenu.value.uid}`)\nif (!subMenu) throwError(COMPONENT_NAME, 'can not inject sub menu')\n\nconst active = computed(() => props.index === rootMenu.activeIndex)\nconst item: MenuItemRegistered = reactive({\n  index: props.index,\n  indexPath,\n  active,\n})\n\nconst handleClick = () => {\n  if (!props.disabled) {\n    rootMenu.handleMenuItemClick({\n      index: props.index,\n      indexPath: indexPath.value,\n      route: props.route,\n    })\n    emit('click', item)\n  }\n}\n\nonMounted(() => {\n  subMenu.addSubMenu(item)\n  rootMenu.addMenuItem(item)\n})\n\nonBeforeUnmount(() => {\n  subMenu.removeSubMenu(item)\n  rootMenu.removeMenuItem(item)\n})\n\ndefineExpose({\n  parentMenu,\n  rootMenu,\n  active,\n  nsMenu,\n  nsMenuItem,\n  handleClick,\n})\n</script>\n"], "names": ["getCurrentInstance", "inject", "useNamespace", "throwError", "useMenu", "computed", "reactive", "onMounted", "onBeforeUnmount"], "mappings": ";;;;;;;;;;;;;uCAwDc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAIA,IAAA,MAAM,WAAWA,sBAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,QAAA,GAAWC,WAAqB,UAAU,CAAA,CAAA;AAChD,IAAM,MAAA,MAAA,GAASC,mBAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,UAAA,GAAaA,mBAAa,WAAW,CAAA,CAAA;AAC3C,IAAA,IAAI,CAAC,QAAA;AAEL,MAAMC,+BAAwB,EAAA,0BAAsB,CAAA,CAAA;AAEpD,IAAA,MAAM,YAAkC,EAAA,SAAA,EAAA,GAAWC,kBAAW,CAAA,QAAA,WAAW,CAAA,KAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACzE,IAAA,MAAK,OAAS,GAAWH,UAAA,CAAA,CAAA,QAAA,EAAA,UAAyC,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAElE,IAAA,IAAA,CAAA;AACA,MAAAE,iBAAiC,cAAS,EAAA,yBAAA,CAAA,CAAA;AAAA,IAAA,YAC3B,GAAAE,YAAA,CAAA,MAAA,KAAA,CAAA,KAAA,KAAA,QAAA,CAAA,WAAA,CAAA,CAAA;AAAA,IACb,MAAA,IAAA,GAAAC,YAAA,CAAA;AAAA,MACA,KAAA,EAAA,KAAA,CAAA,KAAA;AAAA,MACD,SAAA;AAED,MAAA;AACE,KAAI,CAAA,CAAA;AACF,IAAA,MAAA,WAA6B,GAAA,MAAA;AAAA,MAAA,IAC3B,OAAO,QAAM,EAAA;AAAA,QAAA,4BACQ,CAAA;AAAA,UACrB,OAAO,KAAM,CAAA,KAAA;AAAA,UACd,SAAA,EAAA,SAAA,CAAA,KAAA;AACD,UAAA,YAAc,CAAI,KAAA;AAAA,SACpB,CAAA,CAAA;AAAA,QACF,IAAA,CAAA,OAAA,EAAA,IAAA,CAAA,CAAA;AAEA,OAAA;AACE,KAAA,CAAA;AACA,IAAAC,aAAA,CAAA;AAAyB,MAC1B,OAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AAED,MAAA,QAAA,CAAA,WAAsB,CAAA,IAAA,CAAA,CAAA;AACpB,KAAA,CAAA,CAAA;AACA,IAAAC;AAA4B,MAC7B,OAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA;AAED,MAAa,QAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA;AAAA,KACX,CAAA,CAAA;AAAA,IACA,MAAA,CAAA;AAAA,MACA,UAAA;AAAA,MACA,QAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACD,UAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}