{"version": 3, "file": "menu-item.mjs", "sources": ["../../../../../../packages/components/menu/src/menu-item.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isArray,\n  isString,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { RouteLocationRaw } from 'vue-router'\nimport type { MenuItemRegistered } from './types'\n\nexport const menuItemProps = buildProps({\n  /**\n   * @description unique identification\n   */\n  index: {\n    type: definePropType<string | null>([String, null]),\n    default: null,\n  },\n  /**\n   * @description Vue Router object\n   */\n  route: {\n    type: definePropType<RouteLocationRaw>([String, Object]),\n  },\n  /**\n   * @description whether disabled\n   */\n  disabled: Boolean,\n} as const)\nexport type MenuItemProps = ExtractPropTypes<typeof menuItemProps>\n\nexport const menuItemEmits = {\n  click: (item: MenuItemRegistered) =>\n    isString(item.index) && isArray(item.indexPath),\n}\nexport type MenuItemEmits = typeof menuItemEmits\n"], "names": [], "mappings": ";;;AAMY,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACxC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,KAAK,EAAE,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;AAClE;;;;"}