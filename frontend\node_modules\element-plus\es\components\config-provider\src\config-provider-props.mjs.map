{"version": 3, "file": "config-provider-props.mjs", "sources": ["../../../../../../packages/components/config-provider/src/config-provider-props.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { useEmptyValuesProps, useSizeProp } from '@element-plus/hooks'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { Language } from '@element-plus/locale'\nimport type { ButtonConfigContext } from '@element-plus/components/button'\nimport type { MessageConfigContext } from '@element-plus/components/message'\nimport type { LinkConfigContext } from '@element-plus/components/link'\n\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\nexport type ExperimentalFeatures = {\n  // TO BE Defined\n}\n\nexport const configProviderProps = buildProps({\n  /**\n   * @description Controlling if the users want a11y features\n   */\n  a11y: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Locale Object\n   */\n  locale: {\n    type: definePropType<Language>(Object),\n  },\n  /**\n   * @description global component size\n   */\n  size: useSizeProp,\n  /**\n   * @description button related configuration, [see the following table](#button-attributes)\n   */\n  button: {\n    type: definePropType<ButtonConfigContext>(Object),\n  },\n  /**\n   * @description link related configuration, [see the following table](link-attributes)\n   */\n  link: {\n    type: definePropType<LinkConfigContext>(Object),\n  },\n  /**\n   * @description features at experimental stage to be added, all features are default to be set to false                                                                                | ^[object]\n   */\n  experimentalFeatures: {\n    type: definePropType<ExperimentalFeatures>(Object),\n  },\n  /**\n   * @description Controls if we should handle keyboard navigation\n   */\n  keyboardNavigation: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description message related configuration, [see the following table](#message-attributes)\n   */\n  message: {\n    type: definePropType<MessageConfigContext>(Object),\n  },\n  /**\n   * @description global Initial zIndex\n   */\n  zIndex: Number,\n  /**\n   * @description global component className prefix (cooperated with [$namespace](https://github.com/element-plus/element-plus/blob/dev/packages/theme-chalk/src/mixins/config.scss#L1)) | ^[string]\n   */\n  namespace: {\n    type: String,\n    default: 'el',\n  },\n  ...useEmptyValuesProps,\n} as const)\nexport type ConfigProviderProps = ExtractPropTypes<typeof configProviderProps>\n"], "names": [], "mappings": ";;;;AAEY,MAAC,mBAAmB,GAAG,UAAU,CAAC;AAC9C,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,oBAAoB,EAAE;AACxB,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,GAAG,mBAAmB;AACxB,CAAC;;;;"}