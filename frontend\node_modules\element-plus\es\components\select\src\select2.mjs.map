{"version": 3, "file": "select2.mjs", "sources": ["../../../../../../packages/components/select/src/select.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"selectRef\"\n    v-click-outside:[popperRef]=\"handleClickOutside\"\n    :class=\"[nsSelect.b(), nsSelect.m(selectSize)]\"\n    @[mouseEnterEventName]=\"states.inputHovering = true\"\n    @mouseleave=\"states.inputHovering = false\"\n  >\n    <el-tooltip\n      ref=\"tooltipRef\"\n      :visible=\"dropdownMenuVisible\"\n      :placement=\"placement\"\n      :teleported=\"teleported\"\n      :popper-class=\"[nsSelect.e('popper'), popperClass]\"\n      :popper-options=\"popperOptions\"\n      :fallback-placements=\"fallbackPlacements\"\n      :effect=\"effect\"\n      pure\n      trigger=\"click\"\n      :transition=\"`${nsSelect.namespace.value}-zoom-in-top`\"\n      :stop-popper-mouse-event=\"false\"\n      :gpu-acceleration=\"false\"\n      :persistent=\"persistent\"\n      :append-to=\"appendTo\"\n      :show-arrow=\"showArrow\"\n      :offset=\"offset\"\n      @before-show=\"handleMenuEnter\"\n      @hide=\"states.isBeforeHide = false\"\n    >\n      <template #default>\n        <div\n          ref=\"wrapperRef\"\n          :class=\"[\n            nsSelect.e('wrapper'),\n            nsSelect.is('focused', isFocused),\n            nsSelect.is('hovering', states.inputHovering),\n            nsSelect.is('filterable', filterable),\n            nsSelect.is('disabled', selectDisabled),\n          ]\"\n          @click.prevent=\"toggleMenu\"\n        >\n          <div\n            v-if=\"$slots.prefix\"\n            ref=\"prefixRef\"\n            :class=\"nsSelect.e('prefix')\"\n          >\n            <slot name=\"prefix\" />\n          </div>\n          <div\n            ref=\"selectionRef\"\n            :class=\"[\n              nsSelect.e('selection'),\n              nsSelect.is(\n                'near',\n                multiple && !$slots.prefix && !!states.selected.length\n              ),\n            ]\"\n          >\n            <slot v-if=\"multiple\" name=\"tag\">\n              <div\n                v-for=\"item in showTagList\"\n                :key=\"getValueKey(item)\"\n                :class=\"nsSelect.e('selected-item')\"\n              >\n                <el-tag\n                  :closable=\"!selectDisabled && !item.isDisabled\"\n                  :size=\"collapseTagSize\"\n                  :type=\"tagType\"\n                  :effect=\"tagEffect\"\n                  disable-transitions\n                  :style=\"tagStyle\"\n                  @close=\"deleteTag($event, item)\"\n                >\n                  <span :class=\"nsSelect.e('tags-text')\">\n                    <slot\n                      name=\"label\"\n                      :label=\"item.currentLabel\"\n                      :value=\"item.value\"\n                    >\n                      {{ item.currentLabel }}\n                    </slot>\n                  </span>\n                </el-tag>\n              </div>\n\n              <el-tooltip\n                v-if=\"collapseTags && states.selected.length > maxCollapseTags\"\n                ref=\"tagTooltipRef\"\n                :disabled=\"dropdownMenuVisible || !collapseTagsTooltip\"\n                :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n                :effect=\"effect\"\n                placement=\"bottom\"\n                :teleported=\"teleported\"\n              >\n                <template #default>\n                  <div\n                    ref=\"collapseItemRef\"\n                    :class=\"nsSelect.e('selected-item')\"\n                  >\n                    <el-tag\n                      :closable=\"false\"\n                      :size=\"collapseTagSize\"\n                      :type=\"tagType\"\n                      :effect=\"tagEffect\"\n                      disable-transitions\n                      :style=\"collapseTagStyle\"\n                    >\n                      <span :class=\"nsSelect.e('tags-text')\">\n                        + {{ states.selected.length - maxCollapseTags }}\n                      </span>\n                    </el-tag>\n                  </div>\n                </template>\n                <template #content>\n                  <div ref=\"tagMenuRef\" :class=\"nsSelect.e('selection')\">\n                    <div\n                      v-for=\"item in collapseTagList\"\n                      :key=\"getValueKey(item)\"\n                      :class=\"nsSelect.e('selected-item')\"\n                    >\n                      <el-tag\n                        class=\"in-tooltip\"\n                        :closable=\"!selectDisabled && !item.isDisabled\"\n                        :size=\"collapseTagSize\"\n                        :type=\"tagType\"\n                        :effect=\"tagEffect\"\n                        disable-transitions\n                        @close=\"deleteTag($event, item)\"\n                      >\n                        <span :class=\"nsSelect.e('tags-text')\">\n                          <slot\n                            name=\"label\"\n                            :label=\"item.currentLabel\"\n                            :value=\"item.value\"\n                          >\n                            {{ item.currentLabel }}\n                          </slot>\n                        </span>\n                      </el-tag>\n                    </div>\n                  </div>\n                </template>\n              </el-tooltip>\n            </slot>\n            <div\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('input-wrapper'),\n                nsSelect.is('hidden', !filterable),\n              ]\"\n            >\n              <input\n                :id=\"inputId\"\n                ref=\"inputRef\"\n                v-model=\"states.inputValue\"\n                type=\"text\"\n                :name=\"name\"\n                :class=\"[nsSelect.e('input'), nsSelect.is(selectSize)]\"\n                :disabled=\"selectDisabled\"\n                :autocomplete=\"autocomplete\"\n                :style=\"inputStyle\"\n                :tabindex=\"tabindex\"\n                role=\"combobox\"\n                :readonly=\"!filterable\"\n                spellcheck=\"false\"\n                :aria-activedescendant=\"hoverOption?.id || ''\"\n                :aria-controls=\"contentId\"\n                :aria-expanded=\"dropdownMenuVisible\"\n                :aria-label=\"ariaLabel\"\n                aria-autocomplete=\"none\"\n                aria-haspopup=\"listbox\"\n                @keydown.down.stop.prevent=\"navigateOptions('next')\"\n                @keydown.up.stop.prevent=\"navigateOptions('prev')\"\n                @keydown.esc.stop.prevent=\"handleEsc\"\n                @keydown.enter.stop.prevent=\"selectOption\"\n                @keydown.delete.stop=\"deletePrevTag\"\n                @compositionstart=\"handleCompositionStart\"\n                @compositionupdate=\"handleCompositionUpdate\"\n                @compositionend=\"handleCompositionEnd\"\n                @input=\"onInput\"\n                @click.stop=\"toggleMenu\"\n              />\n              <span\n                v-if=\"filterable\"\n                ref=\"calculatorRef\"\n                aria-hidden=\"true\"\n                :class=\"nsSelect.e('input-calculator')\"\n                v-text=\"states.inputValue\"\n              />\n            </div>\n            <div\n              v-if=\"shouldShowPlaceholder\"\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('placeholder'),\n                nsSelect.is(\n                  'transparent',\n                  !hasModelValue || (expanded && !states.inputValue)\n                ),\n              ]\"\n            >\n              <slot\n                v-if=\"hasModelValue\"\n                name=\"label\"\n                :label=\"currentPlaceholder\"\n                :value=\"modelValue\"\n              >\n                <span>{{ currentPlaceholder }}</span>\n              </slot>\n              <span v-else>{{ currentPlaceholder }}</span>\n            </div>\n          </div>\n          <div ref=\"suffixRef\" :class=\"nsSelect.e('suffix')\">\n            <el-icon\n              v-if=\"iconComponent && !showClose\"\n              :class=\"[nsSelect.e('caret'), nsSelect.e('icon'), iconReverse]\"\n            >\n              <component :is=\"iconComponent\" />\n            </el-icon>\n            <el-icon\n              v-if=\"showClose && clearIcon\"\n              :class=\"[\n                nsSelect.e('caret'),\n                nsSelect.e('icon'),\n                nsSelect.e('clear'),\n              ]\"\n              @click=\"handleClearClick\"\n            >\n              <component :is=\"clearIcon\" />\n            </el-icon>\n            <el-icon\n              v-if=\"validateState && validateIcon && needStatusIcon\"\n              :class=\"[\n                nsInput.e('icon'),\n                nsInput.e('validateIcon'),\n                nsInput.is('loading', validateState === 'validating'),\n              ]\"\n            >\n              <component :is=\"validateIcon\" />\n            </el-icon>\n          </div>\n        </div>\n      </template>\n      <template #content>\n        <el-select-menu ref=\"menuRef\">\n          <div\n            v-if=\"$slots.header\"\n            :class=\"nsSelect.be('dropdown', 'header')\"\n            @click.stop\n          >\n            <slot name=\"header\" />\n          </div>\n          <el-scrollbar\n            v-show=\"states.options.size > 0 && !loading\"\n            :id=\"contentId\"\n            ref=\"scrollbarRef\"\n            tag=\"ul\"\n            :wrap-class=\"nsSelect.be('dropdown', 'wrap')\"\n            :view-class=\"nsSelect.be('dropdown', 'list')\"\n            :class=\"[nsSelect.is('empty', filteredOptionsCount === 0)]\"\n            role=\"listbox\"\n            :aria-label=\"ariaLabel\"\n            aria-orientation=\"vertical\"\n            @scroll=\"popupScroll\"\n          >\n            <el-option\n              v-if=\"showNewOption\"\n              :value=\"states.inputValue\"\n              :created=\"true\"\n            />\n            <el-options>\n              <slot />\n            </el-options>\n          </el-scrollbar>\n          <div\n            v-if=\"$slots.loading && loading\"\n            :class=\"nsSelect.be('dropdown', 'loading')\"\n          >\n            <slot name=\"loading\" />\n          </div>\n          <div\n            v-else-if=\"loading || filteredOptionsCount === 0\"\n            :class=\"nsSelect.be('dropdown', 'empty')\"\n          >\n            <slot name=\"empty\">\n              <span>{{ emptyText }}</span>\n            </slot>\n          </div>\n          <div\n            v-if=\"$slots.footer\"\n            :class=\"nsSelect.be('dropdown', 'footer')\"\n            @click.stop\n          >\n            <slot name=\"footer\" />\n          </div>\n        </el-select-menu>\n      </template>\n    </el-tooltip>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, provide, reactive, toRefs, watch } from 'vue'\nimport { ClickOutside } from '@element-plus/directives'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElTag from '@element-plus/components/tag'\nimport ElIcon from '@element-plus/components/icon'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { flattedChildren, isArray, isObject } from '@element-plus/utils'\nimport { useCalcInputWidth } from '@element-plus/hooks'\nimport ElOption from './option.vue'\nimport ElSelectMenu from './select-dropdown.vue'\nimport { useSelect } from './useSelect'\nimport { selectKey } from './token'\nimport ElOptions from './options'\nimport { SelectProps } from './select'\n\nimport type { VNode } from 'vue';\nimport type { SelectContext } from './type'\n\nconst COMPONENT_NAME = 'ElSelect'\nexport default defineComponent({\n  name: COMPONENT_NAME,\n  componentName: COMPONENT_NAME,\n  components: {\n    ElSelectMenu,\n    ElOption,\n    ElOptions,\n    ElTag,\n    ElScrollbar,\n    ElTooltip,\n    ElIcon,\n  },\n  directives: { ClickOutside },\n  props: SelectProps,\n  emits: [\n    UPDATE_MODEL_EVENT,\n    CHANGE_EVENT,\n    'remove-tag',\n    'clear',\n    'visible-change',\n    'focus',\n    'blur',\n    'popup-scroll',\n  ],\n\n  setup(props, { emit, slots }) {\n    const modelValue = computed(() => {\n      const { modelValue: rawModelValue, multiple } = props\n      const fallback = multiple ? [] : undefined\n      // When it is array, we check if this is multi-select.\n      // Based on the result we get\n      if (isArray(rawModelValue)) {\n        return multiple ? rawModelValue : fallback\n      }\n\n      return multiple ? fallback : rawModelValue\n    })\n\n    const _props = reactive({\n      ...toRefs(props),\n      modelValue,\n    })\n\n    const API = useSelect(_props, emit)\n    const { calculatorRef, inputStyle } = useCalcInputWidth()\n\n    const manuallyRenderSlots = (defaultSlots: VNode[] | undefined) => {\n      // After option rendering is completed, the useSelect internal state can collect the value of each option.\n      // If the persistent value is false, option will not be rendered by default, so in this case,\n      // manually render and load option data here.\n      if (!props.persistent && defaultSlots) {\n        const children = flattedChildren(defaultSlots) as VNode[]\n        children.filter((item) => {\n          // @ts-expect-error\n          return isObject(item) && item!.type.name === 'ElOption'\n        }).forEach(item => {\n          const obj = { ...item.props } as any\n          obj.currentLabel = obj.label || (isObject(obj.value) ? '' : obj.value)\n          API.onOptionCreate(obj)\n        })\n      }\n    }\n    watch(() => {\n      const currentSlot = slots.default?.()\n      return currentSlot\n    }, (newSlot) => {\n      manuallyRenderSlots(newSlot)\n    }, {\n      immediate: true,\n    })\n\n    provide(\n      selectKey,\n      reactive({\n        props: _props,\n        states: API.states,\n        selectRef: API.selectRef,\n        optionsArray: API.optionsArray,\n        setSelected: API.setSelected,\n        handleOptionSelect: API.handleOptionSelect,\n        onOptionCreate: API.onOptionCreate,\n        onOptionDestroy: API.onOptionDestroy,\n      }) satisfies SelectContext\n    )\n\n    const selectedLabel = computed(() => {\n      if (!props.multiple) {\n        return API.states.selectedLabel\n      }\n      return API.states.selected.map((i) => i.currentLabel as string)\n    })\n\n    return {\n      ...API,\n      modelValue,\n      selectedLabel,\n      calculatorRef,\n      inputStyle,\n    }\n  },\n})\n</script>\n"], "names": ["ElOption", "_toHandler<PERSON>ey", "_createVNode", "_withCtx", "_createElementVNode", "_normalizeClass", "_openBlock", "_createElementBlock", "_renderSlot", "_createCommentVNode", "_Fragment", "_renderList", "_normalizeStyle", "_createBlock", "_toDisplayString", "_with<PERSON><PERSON><PERSON>", "_withModifiers", "_vModelText", "_resolveDynamicComponent", "_withDirectives", "_vShow"], "mappings": ";;;;;;;;;;;;;;;;;;AAiUA,MAAM,cAAiB,GAAA,UAAA,CAAA;AACvB,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,cAAA;AAAA,EACN,aAAe,EAAA,cAAA;AAAA,EACf,UAAY,EAAA;AAAA,IACV,YAAA;AAAA,cACAA,MAAA;AAAA,IACA,SAAA;AAAA,IACA,KAAA;AAAA,IACA,WAAA;AAAA,IACA,SAAA;AAAA,IACA,MAAA;AAAA,GACF;AAAA,EACA,UAAA,EAAY,EAAE,YAAa,EAAA;AAAA,EAC3B,KAAO,EAAA,WAAA;AAAA,EACP,KAAO,EAAA;AAAA,IACL,kBAAA;AAAA,IACA,YAAA;AAAA,IACA,YAAA;AAAA,IACA,OAAA;AAAA,IACA,gBAAA;AAAA,IACA,OAAA;AAAA,IACA,MAAA;AAAA,IACA,cAAA;AAAA,GACF;AAAA,EAEA,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAM,OAAS,EAAA;AAC5B,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAA,MAAM,EAAE,UAAA,EAAY,aAAe,EAAA,QAAA,EAAa,GAAA,KAAA,CAAA;AAChD,MAAM,MAAA,QAAA,GAAW,QAAW,GAAA,EAAK,GAAA,KAAA,CAAA,CAAA;AAGjC,MAAI,IAAA,OAAA,CAAQ,aAAa,CAAG,EAAA;AAC1B,QAAA,OAAO,WAAW,aAAgB,GAAA,QAAA,CAAA;AAAA,OACpC;AAEA,MAAA,OAAO,WAAW,QAAW,GAAA,aAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAA,MAAM,SAAS,QAAS,CAAA;AAAA,MACtB,GAAG,OAAO,KAAK,CAAA;AAAA,MACf,UAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,GAAA,GAAM,SAAU,CAAA,MAAA,EAAQ,IAAI,CAAA,CAAA;AAClC,IAAA,MAAM,EAAE,aAAA,EAAe,UAAW,EAAA,GAAI,iBAAkB,EAAA,CAAA;AAExD,IAAM,MAAA,mBAAA,GAAsB,CAAC,YAAsC,KAAA;AAIjE,MAAI,IAAA,CAAC,KAAM,CAAA,UAAA,IAAc,YAAc,EAAA;AACrC,QAAM,MAAA,QAAA,GAAW,gBAAgB,YAAY,CAAA,CAAA;AAC7C,QAAS,QAAA,CAAA,MAAA,CAAO,CAAC,IAAS,KAAA;AAExB,UAAA,OAAO,QAAS,CAAA,IAAI,CAAK,IAAA,IAAA,CAAM,KAAK,IAAS,KAAA,UAAA,CAAA;AAAA,SAC9C,CAAE,CAAA,OAAA,CAAQ,CAAQ,IAAA,KAAA;AACjB,UAAA,MAAM,GAAM,GAAA,EAAE,GAAG,IAAA,CAAK,KAAM,EAAA,CAAA;AAC5B,UAAI,GAAA,CAAA,YAAA,GAAe,IAAI,KAAU,KAAA,QAAA,CAAS,IAAI,KAAK,CAAA,GAAI,KAAK,GAAI,CAAA,KAAA,CAAA,CAAA;AAChE,UAAA,GAAA,CAAI,eAAe,GAAG,CAAA,CAAA;AAAA,SACvB,CAAA,CAAA;AAAA,OACH;AAAA,KACF,CAAA;AACA,IAAA,KAAA,CAAM,MAAM;AACV,MAAM,IAAA,EAAA,CAAA;AACN,MAAO,MAAA,WAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACT,OAAgB,WAAA,CAAA;AACd,KAAA,EAAA,CAAA,OAAA,KAAA;AAA2B,MAC1B,mBAAA,CAAA,OAAA,CAAA,CAAA;AAAA,KAAA,EACU;AAAA,MACZ,SAAA,EAAA,IAAA;AAED,KAAA,CAAA,CAAA;AAAA,IACE,OAAA,CAAA,SAAA,EAAA,QAAA,CAAA;AAAA,MACA,KAAS,EAAA,MAAA;AAAA,MAAA,MACA,EAAA,GAAA,CAAA,MAAA;AAAA,MAAA,WACK,GAAA,CAAA,SAAA;AAAA,MAAA,cACG,GAAA,CAAA,YAAA;AAAA,MAAA,gBACD,CAAI,WAAA;AAAA,MAAA,kBACD,EAAA,GAAA,CAAA,kBAAA;AAAA,MAAA,kCACO;AAAA,MAAA,oBACJ,CAAA,eAAA;AAAA,KAAA,CAAA,CAAA,CACpB;AAAqB,IAAA,MACtB,aAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACH,IAAA,CAAA,KAAA,CAAA,QAAA,EAAA;AAEA,QAAM,OAAA,GAAA,CAAA,MAAA,CAAgB,aAAe,CAAA;AACnC,OAAI;AACF,MAAA,OAAA,UAAkB,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,YAAA,CAAA,CAAA;AAAA,KACpB,CAAA,CAAA;AACA,IAAA,OAAA;AAA8D,MAC/D,GAAA,GAAA;AAED,MAAO,UAAA;AAAA,MACL,aAAG;AAAA,MACH,aAAA;AAAA,MACA,UAAA;AAAA,KACA,CAAA;AAAA,GACA;AAAA,CACF,CAAA,CAAA;AAEJ,SAAC,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA;;;;;;;;;;;wCA5HO,CAAA,CAAA,EAAA,EAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,IAxSJ,CAAIC,YAAA,CAAA,IAAA,CAAA,mBAAA,CAAA,GAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,aAAA,GAAA,IAAA;AAAA,IAEH,YAAQ,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAS,aAAK,GAAA,KAAW;AAAU,GAC3C,EAAA;AAA2C,IAC3CC,WAAA,CAAA,qBAAY,EAAA;AAAoB,MAAA,GAAA,EAAA,YAAA;MAmSpB,OAAA,EAAA,IAAA,CAAA,mBAAA;AAAA,MAhSX,SAAI,EAAA,IAAA,CAAA,SAAA;AAAA,MACH,UAAS,EAAA,IAAA,CAAA,UAAA;AAAA,MACT,cAAW,EAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA;AAAA,MACX,gBAAY,EAAA,IAAA,CAAA,aAAA;AAAA,MACZ,qBAAwB,EAAA,IAAA,CAAA,kBAAwB;AAAA,MAChD,MAAgB,EAAA,IAAA,CAAA,MAAA;AAAA,MAChB,IAAqB,EAAA,EAAA;AAAA,MACrB,OAAQ,EAAA,OAAA;AAAA,MACT,UAAA,EAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,MACA,yBAAQ,EAAA,KAAA;AAAA,MACP,kBAAwB,EAAA,KAAA;AAAe,MACvC,UAAyB,EAAA,IAAA,CAAA,UAAA;AAAA,MACzB,WAAkB,EAAA,IAAA,CAAA,QAAA;AAAA,MAClB,YAAY,EAAA,IAAA,CAAA,SAAA;AAAA,MACZ,MAAW,EAAA,IAAA,CAAA,MAAA;AAAA,MACX,YAAY,EAAA,IAAA,CAAA,eAAA;AAAA,MACZ,MAAQ,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,YAAA,GAAA,KAAA;AAAA,KAAA,EACK;AAAA,MACb,OAAA,EAAMC,OAAA,CAAA,MAAA;AAAmB,QAAA,IAAA,EAAA,CAAA;AAEf,QAAA,OAAO;AAoNV,UAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,YAlNA,GAAA,EAAA,YAAA;AAAA,YACE,KAAA,EAAAC,cAAA,CAAA;AAAA,2BAA0B,CAAA,CAAA,CAAA,SAAA,CAAA;AAAA,cAAyB,IAAA,CAAA,QAAW,CAAA,EAAA,CAAA,SAAY,EAAS,IAAA,CAAA,SAAA,CAAA;AAAA,cAAwB,IAAA,CAAA,QAAA,CAAA,EAAe,CAAA,UAAA,EAAA,IAAA,CAAA,MAAoB,CAAA,aAAA,CAAA;AAAA,cAAe,IAAA,CAAA,QAAW,CAAA,EAAA,CAAA,YAAe,EAAU,IAAA,CAAA,UAAA,CAAA;AAAA,cAAe,IAAA,CAAA,QAAW,CAAA,EAAA,CAAA,UAAa,EAAc,IAAA,CAAA,cAAA,CAAA;AAAA,aAAA,CAAA;AAO3P,YAAA,OAAK,eAAoB,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,SAAA,CAAA,CAAA;AAAA,WAAA,EAAA;AAGlB,YAAA,IAAA,CAAA,MADR,CAAA,MAAA,IAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cAMM,GAAA,EAAA,CAAA;AAAA,cAAA,GAAA,EAAA,WAAA;AAAA,cAAA,KAAA,EAAAF,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA;eAJA;AAAA,cACHG,UAAO,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAU,aAAA,EAAA,CAAA,CAAA,IAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;;cAElB,GAAsB,EAAA,cAAA;AAAA,cAAA,KAAA,EAAAJ,cAAA,CAAA;;;;AAExB,aAAA,EAAA;AAAA,cAmKM,IAAA,CAAA,QAAA,GAAAG,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,KAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA,MAAA;AAAA,iBAAAF,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAG,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,IAAA,KAAA;AAAA,kBAlKA,OAAAL,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,oBACE,GAAA,EAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA;AAAA,yCAA4B,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,mBAAsC,EAAA;AAAA,oBAAAL,WAAA,CAAA,iBAAA,EAAA;AAA4C,sBAAA,QAAA,EAAA,CAAA,IAAa,CAAA,cAAa,IAAA,CAAA,eAAa;AAAS,sBAAA,IAAA,EAAA,IAAA,CAAA,eAAA;;;;sBAQpK,KAAA,EAAAU;AAqFO,sBApFL,OAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,SAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,qBAwBM,EAAA;AAAA,sBAAA,OAAA,EAAAT,OAAA,CAAA,MAAA;AAAA,wBAvBWC,kBAAA,CAAA,MAAJ,EAAA;AADb,0BAAA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;AAAA,yBAwBM,EAAA;AAAA,0BAAAG,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA;AAAA,4BAtBH,wBAAqB;AAAA,4BACrB,KAAO,EAAA,IAAA,CAAA,KAAA;AAAU,2BAAA,EAAA,MAAA;;2BAoBT,CAAA;AAAA,yBAAA,EAjBE,CAAA,CAAA;AAA2B,uBAAA,CAAA;AAC7B,sBAAA,CAAA,EAAA,CACN;AAAM,qBAAA,EAAA,IACE,EAAA,CAAA,UAAA,EAAA,MAAA,EAAA,MAAA,EAAA,QAAA,EAAA,OAAA,EAAA,SAAA,CAAA,CAAA;AAAA,mBACT,EAAA,CAAA,CAAA,CAAA;AAAA,iBACC,CAAA,EAAA,GAAA,CAAA;AAAe,gBAAA,IAAA,CAAA,YACV,IAAA,IAAA,CAAA,MAAY,CAAA,QAAA,CAAA,MAAA,GAAA,IAAY,CAAA,eAAA,IAAAF,SAAA,EAAA,EAAAO,WAAA,CAAA,qBAAA,EAAA;AAAA,kBAAA,GAAA,EAAA,CAAA;;AAUvB,kBARP,QAAA,EAAA,IAAA,CAAA,mBAAA,IAAA,CAAA,IAAA,CAAA,mBAAA;AAAA,kBAQO,qBAAA,EAAA,CAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,CAAA;AAAA,kBAAA,MAAA,EAAA,IAAA,CAAA,MAAA;AAAA,kBARA,SAAA,EAAA,QAAA;AAAiB,kBAAA,UAAA,EAAA,IAAA,CAAA,UAAA;;kCAOf,CAAA,MAAA;AAAA,oBAAAT,wBAJQ,EAAA;AAAA,sBAAA,GAAA,EAAA,iBACA;AAAA,sBAAA,KAAA,EAAAC,cAGR,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,qBAAA,EAAA;AADF,sBAAAH,WAAA,CAAA,iBAAA,EAAA;AAAiB,wBAAA,QAAA,EAAA,KAAA;AAAA,wBAAA,IAAA,EAAA,IAAA,CAAA,eAAA;AAAA,wBAAA,IAAA,EAAA,IAAA,CAAA,OAAA;AAAA,wBAAA,MAAA,EAAA,IAAA,CAAA,SAAA;;;;;;;;;;;;;;;;;AAOpB,qBAAA,EAAA;AAwDK,uBAAAI,SAAA,CAAA,IAAA,CAAA,EAAAC,kBAAA,CAAAG,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,eAAA,EAAA,CAAA,IAAA,KAAA;wBAvDP,OAAAL,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,0BACH;AAAkC,0BACb,KAAA,EAAAF,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,yBACb,EAAA;AAAA,0BACCH,WAAA,CAAA,iBAAA,EAAA;AAAA,4BACG,KAAA,EAAA,YAAA;AAAA,4BAAA,QAAA,EAAA,CAAA,IAAA,CAAA,cAAA,IAAA,CAAA,IAAA,CAAA,UAAA;AAEF,sCAiBH,CAAA,eAAA;AAAA,4BAhBN,IAAA,EAAA,IAAA,CAAA,OAAA;AAAA,4BAgBM,MAAA,EAAA,IAAA,CAAA,SAAA;AAAA,4BAAA,qBAAA,EAAA,EAAA;AAAA,4BAfA,OAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,SAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,2BACH,EAAA;AAAiB,4BAAA,OAAA,EAAAC,OAAA,CAAA,MAAA;;gCAaT,KAAA,EAAAE,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;AAAA,+BAVI,EAAA;AAAA,gCACJG,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA;AAAA,kCACA,KAAA,EAAA,IAAA,CAAA,YAAA;AAAA,kCACE,KAAA,EAAA,IAAA,CAAA,KAAA;AAAA,iCACT,EAAA,MAAA;AAAA,iDACwB,CAAAM,eAAA,CAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAAA,iCAAA,CAAA;;AAIjB,6BAFP,CAAA;AAAA,4BAEO,CAAA,EAAA,CAAA;AAAA,2BAAA,EAAA,IAAA,EAAA,CAAA,UAAA,EAAA,MAAA,EAAA,MAAA,EAAA,QAAA,EAAA,SAAA,CAAA,CAAA;AAAA,yBAFA,EAAA,CAAA,CAAA,CAAA;AAAiB,uBAAA,CAAA,EAAA,GAAA,CAAA;AAAe,qBAAA,EAAA,CAAA,CAAA;AACQ,mBAAA,CAAA;AAAA,kBAAA,CAAA,EAAA,CAAA;AAAA,iBAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,EAAA,YAAA,CAAA,CAAA,IAAAL,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,eAAA,CAAA,GAAAA,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;;;;;;;;;AAK1C,kBAAA,EAAA,EAAA,IAAA,CAAO;AA2BV,kBA1BN,GAAA,EAAA,UAAA;AAAA,kBA0BM,qBAAA,EAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAA,UAAA,GAAA,MAAA;AAAA,kBAAA,IAAA,EAAA,MAAA;AAAA,kBAAA,IAAA,EA1BD,IAAI,CAAA,IAAA;AAAA,kBAAc,KAAA,EAAAJ,cAAO,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,OAAU,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,kBAAA,QAAA,EAAA,IAAA,CAAA,cAAA;;AACtC,kBAAA,KAAA,EAAAO,cAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,kBAwBM,QAAA,EAAA,IAAA,CAAA,QAAA;AAAA,kBAAA,IAAA,EAAA,UAAA;AAAA,kBAvBW,QAAA,EAAA,CAAA,IAAA,CAAA,UAAA;AADjB,kBAAA,UAAA,EAAA,OAAA;AAAA,kBAwBM,uBAAA,EAAA,CAAA,CAAA,EAAA,GAAA,IAAA,CAAA,WAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,EAAA,KAAA,EAAA;AAAA,kBAAA,eAAA,EAAA,IAAA,CAAA,SAAA;AAAA,kBAtBH,eAAA,EAAA,wBAAqB;AAAA,kBACrB,YAAA,EAAA,IAAA,CAAA,SAAO;AAAU,kBAAA,mBAAA,EAAA,MAAA;;;AAoBT,oBAAAG,QAAA,CAAAC,aAjBD,CAAA,CAAA,MAAA,KAAA,IAAA,CAAA,eAAA,CAAA,MAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,oBAAAD,QAAA,CAAAC,aACG,CAAG,CAAc,MAAA,KAAA,IAAA,CAAA,eAAU,CAAA,MAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA;AAAA,oBAAAD,QAAA,CAAAC,aAC7B,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA;AAAA,oBAAAD,QAAA,CAAAC,aACA,CAAA,IAAA,CAAA,YAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA;AAAA,oBAAAD,QAAA,CAAAC,aACE,CAAA,IAAA,CAAA,aAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,QAAA,CAAA,CAAA;AAAA,mBACT;AAAA,kBAAA,kBACM,EAAA,IAAA,CAAA,sBAAY;AAAY,kBAAA,mBAAA,EAAA,IAAA,CAAA,uBAAA;6DAUvB;AAAA,kBARP,OAAA,EAAA,IAAA,CAAA,OAAA;AAAA,kBAQO,OAAA,EAAAA,aAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,IAAA,EAAA,qBAAA,EAAA,MAAA,EAAA,UAAA,EAAA,cAAA,EAAA,UAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,eAAA,EAAA,eAAA,EAAA,YAAA,EAAA,WAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,kBAAA,EAAA,SAAA,EAAA,SAAA,CAAA,CAAA,EAAA;AAAA,kBARA,CAAAC,UAAA,EAAA,IAAA,CAAA,MAAA,CAAA,UAAO,CAAA;AAAU,iBAAA,CAAA;;;AAOf,kBAAA,GAAA,EAAA,eAAA;AAJQ,kBAAA,aAAA,EAAA,MAAA;AACA,kBAAA,KAAA,EAAAZ,cAAA,CAAA,IAGR,CAAA,QAAA,CAAA,CAAA,CAAA,kBAAA,CAAA,CAAA;AAAA,kBAAA,WAAA,EAAAS,eAAA,CAAA,IAAA,CAAA,MAAA,CAAA,UAAA,CAAA;AADF,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA,aAAA,CAAA,CAAA,IAAAL,kBAAiB,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,eAAA,EAAA,CAAA,CAAA;AAAA,cAAA,IAAA,CAAA,qBAAA,IAAAH,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,gBAAA,GAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAAF,cAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AASpC,gBAAA,CAAA,EAAA,CAAA;AAAA,eA6CM,EAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,IAAAI,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,cAAA,IAAA,CAAA,SAAA,IAAA,IAAA,CAAA,SAAA,IAAAH,SAAA,EAAA,EAAAO,WAAA,CAAA,kBAAA,EAAA;AAAA,gBAAA,GA5CE,EAAA,CAAA;AAAA,gBAAA,qBAA8B,CAAA;AAAA,kBAAA,gBAA4C,OAAC,CAAA;AAAA,kBAAmC,IAAA,CAAA,QAAA,CAAA,CAAA,CAAS,MAAE,CAAA;AAAsB,kBAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,OAAA,CAAA;;;;AAoCnJ,gBAAA,OA7BK,EAAAV,OAAA,CAAA,MAAA;AAAA,mBAAAG,SACD,EAAA,EAAAO,WAAA,CAAAK,uBAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;AAAA,iBAAA,CAAA;AACsB,gBAAA,CAAA,EAAA,CAC1B;AAAK,eAAA,EAAA,CAAA,EACJ,CAAM,OAAA,EAAA,SAAA,CAAA,CAAA,IAAAT,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,cAAA,IAAA,CAAA,aACE,IAAA,IAAA,CAAA,YAAA,IAAA,IAAqB,CAAA,cAAA,IAAAH,SAAsB,EAAA,EAAAO,WAAA,CAAA,kBAAA,EAAA;AAAA,gBAAA,GAAA,EACzC,CAAA;AAAA,gBAAA,KACI,EAAAR,cAAA,CAAA;AAAA,kBACd,IAAA,CAAA;AAAiB,kBAAA,IACP,CAAA,OAAA,CAAA,CAAA,CAAA,cAAA,CAAA;AAAA,kBAAA,IACN,CAAA,OAAA,CAAA,EAAA,CAAA,SAAA,EAAA,IAAA,CAAA,aAAA,KAAA,YAAA,CAAA;AAAA,iBAAA,CAAA;AACO,eAAA,EAAA;AACD,gBACV,OAAA,EAAAF,OAAA,CAAA,MAAA;AAAsC,mBAAAG,SACvB,EAAA,EAAAO,WAAA,CAAAK,uBAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA;AAAA,iBAAA,CAAA;AACA,gBAAA,CAAA,EAAA,CACf;AAAY,eAAA,EAAA,CAAA,EACb,CAAkB,OAAA,CAAA,CAAA,IAAAT,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,aAAA,EAAA,CAAA,CAAA;AACJ,WAAA,EAAA,EAAA,EAAA,CAAA,SACN,CAAA,CAAA;AAAA,SAAA,CAAA;AAAmC,OAAA,CAAA;AACF,MAAA,OAAA,EAAAN,OAAA,CAAA,MAAA;AACL,QAAAD,WAAA,CAAA,yBAAA,EACK,EAAA,GAAA,EAAA,SAAA,EAAA,EAAA;AAAA,UAAA,OAAA,EAAAC,OAAA,CAAA,MAAA;AACN,YAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAG,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;oBAClC;AAAkB,cAAA,KAAA,EACCF,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,cAAA,OACH,EAAAW,aAAA,CAAA,MAAA;AAAA,eAAA,EAAA,CAAA,MACT,CAAA,CAAA;AAAA,aACP,EAAA;AAAsB,cAAAR,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AA1Bd,aAAA,EAAA,EAAA,EAAA,CAAA,SAAA,CAAA,CAAA,IAAAC,kBAAiB,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,YAAAU,cAAA,CAAAjB,WAAA,CAAA,uBAAA,EAAA;AA6BpB,cAAA,EAAA,EAAA,IAAA,CAAA,SAAA;AAKN,cAAA,GAAA,EAAA,cAAA;uBAJI;AAAA,cAAA,YACQ,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,MAAA,CAAA;AAAA,cACX,YAAA,EAAO,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAS,MAAC,CAAA;AAAA,cAClB,KAAA,EAAAG,cAAA,CAAA,CAAA,IAAA,CAAA,QAA0B,CAAA,EAAA,CAAA,OAAX,EAAU,IAAA,CAAA,oBAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAAA,cAAA,IAAA,EAAA,SAAA;;;;;cAIrB,OADR,EAAAF,OAAA,CAAA,MAAA;AAAA,gBAoBM,IAAA,CAAA,aAAA,IAAAG,SAAA,EAAA,EAAAO,WAAA,CAAA,oBAAA,EAAA;AAAA,kBAAA,GAAA,EAAA,CAAA;AAAA,kBAAA,KAAA,EAAA,IAAA,CAAA,MAAA,CAAA,UAAA;kBAlBH,OAAK,EAAA,IAAA;AAAA,iBAAA,EAAA,iBAA8B,CAAA,CAAA,IAAAJ,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,gBAAAP,iCAA6C,EAAA,IAAA,EAAA;AAAA,kBAAA,OAA0C,EAAAC,OAAA,CAAA,MAAA;AAAA,oBAAAK,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;;AAA8F,kBAAA,CAAA,EAAA,CAAA;;;;AAUjN,aAAA,EAAA,CAAA,EAAA,CAAA,IAAA,EAAA,YAAA,EADR,YAOO,EAAA,OAAA,EAAA,YAAA,EAAA,UAAA,CAAA,CAAA,EAAA;AAAA,cAAA,CAAAY,KAAA,EAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA;;AAJG,YAAA,IAAA,CAAA,MACA,CAAA,OAAA,IAAA,IAAA,CAAA,OAAA,IAAAd,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAGH,cADL,KAAA,EAAAF,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,SAAA,CAAA,CAAA;AAAA,aAAqC,EAAA;AAAA,cAAAG,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,aAAA,EAAA,CAAA,CAAA,IAAA,IAAA,CAAA,OAAA,IAAA,IAAV,CAAA,oBAAA,KAAA,CAAA,IAAAF,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAAF,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,OAAA,CAAA,CAAA;AAAA,aAAA,EAAA;AAAA,cAE7BG,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,OAAA,EAAA,EAAA,EAAA,MAAA;AAAA,gBAA4CJ,kBAAA,CAAA,MAAA,EAAA,IAAA,EAAAU,eAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AAAA,eAAA,CAAA;AAAA,aAAA,EAAA,CAAA,CAAA,IAAAL,kBAAV,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,YAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAH,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAAA,cAAA,KAAA,EAAAF,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,cAAA,OAAA,EAAAW,aAAA,CAAA,MAAA;;;;;;;;AAGtC,OAAA,CAAA;AAAA,MA4BM,CAAA,EAAA,CAAA;AAAA,KAAA,EAAA,CAAA,EAAA,CAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,YAAA,EAAA,YAAA,EAAA,WAAA,EAAA,YAAA,EAAA,QAAA,EAAA,cAAA,EAAA,QAAA,CAAA,CAAA;AAAA,GAAA,EAAA,EAAA,EAAA,CAAA,cA5BG,CAAA,CAAA,GAAA;AAAA,IAAa,CAAA,wBAAO,EAAA,IAAA,CAAA,kBAAU,EAAA,IAAA,CAAA,SAAA,CAAA;AAAA,GAAA,CAAA,CAAA;;AAM3B,aAAA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,YAAA,CAAA,CAAA,CAAA;;;;"}