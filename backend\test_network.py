#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试网络连接
"""

import asyncio
import httpx

async def test_network():
    """测试网络连接"""
    
    print("🔍 测试网络连接...")
    
    # 测试基本的HTTP连接
    try:
        print("1. 测试基本HTTP连接...")
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get("https://httpbin.org/get")
            print(f"   ✅ HTTP连接正常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ HTTP连接失败: {e}")
        return
    
    # 测试SiliconFlow API服务器连接
    try:
        print("2. 测试SiliconFlow API服务器...")
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get("https://api.siliconflow.cn")
            print(f"   ✅ SiliconFlow服务器连接正常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ SiliconFlow服务器连接失败: {e}")
        return
    
    # 测试API端点
    try:
        print("3. 测试API端点（无认证）...")
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(
                "https://api.siliconflow.cn/v1/chat/completions",
                headers={"Content-Type": "application/json"},
                json={"model": "test", "messages": []}
            )
            print(f"   📊 API端点响应: {response.status_code}")
            if response.status_code == 401:
                print("   ✅ API端点正常（需要认证）")
            else:
                print(f"   ⚠️ 意外响应: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ API端点测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_network())
