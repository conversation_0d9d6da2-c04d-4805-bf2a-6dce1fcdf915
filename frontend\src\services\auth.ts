// 认证服务
import api from './api'
import type { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  User 
} from '@/types'

export class AuthService {
  // 用户登录
  static async login(credentials: LoginRequest): Promise<AuthResponse> {
    const formData = new FormData()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)
    
    const response = await api.post<AuthResponse>('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
    
    // 保存token和用户信息
    if (response.data.access_token) {
      localStorage.setItem('access_token', response.data.access_token)
      localStorage.setItem('user', JSON.stringify(response.data.user))
    }
    
    return response.data
  }

  // 用户注册
  static async register(userData: RegisterRequest): Promise<User> {
    const response = await api.post<User>('/auth/register', userData)
    return response.data
  }

  // 获取当前用户信息
  static async getCurrentUser(): Promise<User> {
    const response = await api.get<User>('/auth/me')
    
    // 更新本地存储的用户信息
    localStorage.setItem('user', JSON.stringify(response.data))
    
    return response.data
  }

  // 刷新token
  static async refreshToken(): Promise<AuthResponse> {
    const response = await api.post<AuthResponse>('/auth/refresh')
    
    if (response.data.access_token) {
      localStorage.setItem('access_token', response.data.access_token)
      localStorage.setItem('user', JSON.stringify(response.data.user))
    }
    
    return response.data
  }

  // 用户登出
  static logout(): void {
    localStorage.removeItem('access_token')
    localStorage.removeItem('user')
  }

  // 检查是否已登录
  static isAuthenticated(): boolean {
    const token = localStorage.getItem('access_token')
    return !!token
  }

  // 获取本地存储的用户信息
  static getLocalUser(): User | null {
    const userStr = localStorage.getItem('user')
    if (userStr) {
      try {
        return JSON.parse(userStr)
      } catch {
        return null
      }
    }
    return null
  }

  // 获取token
  static getToken(): string | null {
    return localStorage.getItem('access_token')
  }
}
