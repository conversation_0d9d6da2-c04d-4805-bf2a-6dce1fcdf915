#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速添加缺失字段
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db.database import engine

def add_missing_fields():
    """添加缺失字段"""
    
    print("🔧 添加缺失字段...")
    
    with engine.connect() as conn:
        try:
            # 添加 personal_meaning 字段
            print("添加 personal_meaning 字段...")
            conn.execute(text("ALTER TABLE tattoo_requests ADD COLUMN personal_meaning TEXT"))
            conn.commit()
            print("✅ personal_meaning 字段添加成功")
        except Exception as e:
            print(f"personal_meaning 字段可能已存在: {e}")
        
        try:
            # 添加 size_preference 字段
            print("添加 size_preference 字段...")
            conn.execute(text("ALTER TABLE tattoo_requests ADD COLUMN size_preference VARCHAR(50)"))
            conn.commit()
            print("✅ size_preference 字段添加成功")
        except Exception as e:
            print(f"size_preference 字段可能已存在: {e}")
        
        # 检查最终表结构
        print("📋 最终表结构:")
        result = conn.execute(text("DESCRIBE tattoo_requests"))
        for row in result.fetchall():
            print(f"  {row[0]}: {row[1]}")

if __name__ == "__main__":
    add_missing_fields()
