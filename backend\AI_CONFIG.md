# AI模型配置说明

本项目支持灵活配置不同的AI服务商和模型，通过环境变量进行配置。

## 文本AI模型配置

用于汉字和成语推荐的文本AI模型配置：

### 环境变量

```bash
# 文本AI模型配置
TEXT_AI_API_URL=https://api.siliconflow.cn/v1
TEXT_AI_API_KEY=your_text_ai_api_key_here
TEXT_AI_MODEL=deepseek-ai/DeepSeek-R1
```

### 支持的服务商

#### 1. SiliconFlow (推荐)
```bash
TEXT_AI_API_URL=https://api.siliconflow.cn/v1
TEXT_AI_API_KEY=your_siliconflow_api_key
TEXT_AI_MODEL=deepseek-ai/DeepSeek-R1
```

#### 2. OpenAI
```bash
TEXT_AI_API_URL=https://api.openai.com/v1
TEXT_AI_API_KEY=your_openai_api_key
TEXT_AI_MODEL=gpt-4
```

#### 3. 其他兼容OpenAI API的服务商
```bash
TEXT_AI_API_URL=https://your-custom-api-url/v1
TEXT_AI_API_KEY=your_custom_api_key
TEXT_AI_MODEL=your_custom_model_name
```

## 图像AI模型配置

用于纹身图片生成的图像AI模型配置：

### 环境变量

```bash
# 图像AI模型配置
IMAGE_AI_API_URL=https://api.stability.ai/v1
IMAGE_AI_API_KEY=your_image_ai_api_key_here
IMAGE_AI_MODEL=stable-diffusion-xl-1024-v1-0
```

### 支持的服务商

#### 1. Stability AI
```bash
IMAGE_AI_API_URL=https://api.stability.ai/v1
IMAGE_AI_API_KEY=your_stability_ai_api_key
IMAGE_AI_MODEL=stable-diffusion-xl-1024-v1-0
```

#### 2. OpenAI DALL-E
```bash
IMAGE_AI_API_URL=https://api.openai.com/v1
IMAGE_AI_API_KEY=your_openai_api_key
IMAGE_AI_MODEL=dall-e-3
```

## 配置切换示例

### 切换到OpenAI GPT-4
```bash
TEXT_AI_API_URL=https://api.openai.com/v1
TEXT_AI_API_KEY=sk-your-openai-api-key
TEXT_AI_MODEL=gpt-4
```

### 切换到本地部署的模型
```bash
TEXT_AI_API_URL=http://localhost:8080/v1
TEXT_AI_API_KEY=local-api-key
TEXT_AI_MODEL=local-chinese-model
```

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要提交到代码仓库
2. **模型兼容性**: 确保选择的模型支持中文和文化内容理解
3. **API限制**: 注意不同服务商的API调用限制和计费方式
4. **备用方案**: 系统内置了备用推荐方案，当AI服务不可用时自动启用

## 测试配置

可以通过以下方式测试AI配置是否正确：

```bash
# 启动后端服务
uvicorn main:app --reload

# 调用推荐API测试
curl -X POST "http://localhost:8000/api/v1/characters/recommend" \
  -H "Content-Type: application/json" \
  -d '{
    "keywords": [],
    "description": "我想要一个代表勇气的纹身",
    "type": "both",
    "max_results": 3
  }'
```

如果配置正确，应该返回AI推荐的汉字和成语结果。
