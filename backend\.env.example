# 汉字纹身AI平台环境配置示例
# Chinese Character Tattoo AI Platform Environment Configuration Example

# 基础配置
PROJECT_NAME=汉字纹身AI平台
VERSION=1.0.0
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/chinese_tattoo_ai
POSTGRES_SERVER=localhost
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password_here
POSTGRES_DB=chinese_tattoo_ai
POSTGRES_PORT=5432

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI服务API密钥
OPENAI_API_KEY=your_openai_api_key_here
STABILITY_AI_API_KEY=your_stability_ai_api_key_here

# 文件存储配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# 支付配置
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key

# 跨域配置
ALLOWED_HOSTS=["http://localhost:3000", "http://localhost:5173"]

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_TLS=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
