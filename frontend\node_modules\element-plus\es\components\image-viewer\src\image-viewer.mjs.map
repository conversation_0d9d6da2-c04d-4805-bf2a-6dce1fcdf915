{"version": 3, "file": "image-viewer.mjs", "sources": ["../../../../../../packages/components/image-viewer/src/image-viewer.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isNumber,\n  mutable,\n} from '@element-plus/utils'\n\nimport type { Component, ExtractPropTypes } from 'vue'\nimport type ImageViewer from './image-viewer.vue'\n\nexport type ImageViewerAction =\n  | 'zoomIn'\n  | 'zoomOut'\n  | 'clockwise'\n  | 'anticlockwise'\n\nexport const imageViewerProps = buildProps({\n  /**\n   * @description preview link list.\n   */\n  urlList: {\n    type: definePropType<string[]>(Array),\n    default: () => mutable([] as const),\n  },\n  /**\n   * @description preview backdrop z-index.\n   */\n  zIndex: {\n    type: Number,\n  },\n  /**\n   * @description the initial preview image index, less than or equal to the length of `url-list`.\n   */\n  initialIndex: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description whether preview is infinite.\n   */\n  infinite: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether user can emit close event when clicking backdrop.\n   */\n  hideOnClickModal: Boolean,\n  /**\n   * @description whether to append image itself to body. A nested parent element attribute transform should have this attribute set to `true`.\n   */\n  teleported: Boolean,\n  /**\n   * @description whether the image-viewer can be closed by pressing ESC.\n   */\n  closeOnPressEscape: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description the zoom rate of the image viewer zoom event.\n   */\n  zoomRate: {\n    type: Number,\n    default: 1.2,\n  },\n  /**\n   * @description the min scale of the image viewer zoom event.\n   */\n  minScale: {\n    type: Number,\n    default: 0.2,\n  },\n  /**\n   * @description the max scale of the image viewer zoom event.\n   */\n  maxScale: {\n    type: Number,\n    default: 7,\n  },\n  /**\n   * @description show preview image progress content.\n   */\n  showProgress: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description set HTML attribute: crossorigin.\n   */\n  crossorigin: {\n    type: definePropType<'anonymous' | 'use-credentials' | ''>(String),\n  },\n} as const)\nexport type ImageViewerProps = ExtractPropTypes<typeof imageViewerProps>\n\nexport const imageViewerEmits = {\n  close: () => true,\n  switch: (index: number) => isNumber(index),\n  rotate: (deg: number) => isNumber(deg),\n}\nexport type ImageViewerEmits = typeof imageViewerEmits\n\nexport interface ImageViewerMode {\n  name: string\n  icon: Component\n}\n\nexport type ImageViewerInstance = InstanceType<typeof ImageViewer> & unknown\n"], "names": [], "mappings": ";;;;AAMY,MAAC,gBAAgB,GAAG,UAAU,CAAC;AAC3C,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,gBAAgB,EAAE,OAAO;AAC3B,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,CAAC,EAAE;AACS,MAAC,gBAAgB,GAAG;AAChC,EAAE,KAAK,EAAE,MAAM,IAAI;AACnB,EAAE,MAAM,EAAE,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC;AACpC,EAAE,MAAM,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC;AAChC;;;;"}