// 汉字纹身AI平台 - TypeScript类型定义

export interface User {
  id: number
  email: string
  username: string
  full_name?: string
  is_active: boolean
  is_verified: boolean
  is_premium: boolean
  credits: number
  avatar_url?: string
  bio?: string
  language_preference: string
  created_at: string
  last_login?: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  email: string
  username: string
  password: string
  full_name?: string
}

export interface AuthResponse {
  access_token: string
  token_type: string
  user: User
}

export interface ChineseCharacter {
  id: number
  character: string
  pinyin: string
  meaning_en: string
  meaning_zh: string
  cultural_context?: string
  category?: string
  tags?: string[]
  tattoo_popularity: number
  recommended_positions?: string[]
  is_active: boolean
  is_verified: boolean
  created_at: string
}

export interface ChineseIdiom {
  id: number
  idiom: string
  pinyin: string
  meaning_en: string
  meaning_zh: string
  origin_story?: string
  cultural_context?: string
  category?: string
  tags?: string[]
  character_count: number
  difficulty_level: number
  tattoo_popularity: number
  recommended_positions?: string[]
  is_active: boolean
  is_verified: boolean
  created_at: string
}

export interface RecommendationRequest {
  keywords: string[]
  description?: string
  type: 'character' | 'idiom' | 'both'
  max_results?: number
}

export interface CharacterRecommendation {
  character: string
  pinyin: string
  meaning_en: string
  meaning_zh: string
  cultural_context?: string
  score: number
  explanation: string
}

export interface IdiomRecommendation {
  idiom: string
  pinyin: string
  meaning_en: string
  meaning_zh: string
  origin_story?: string
  score: number
  explanation: string
}

export interface RecommendationResponse {
  characters: CharacterRecommendation[]
  idioms: IdiomRecommendation[]
  ai_explanation: string
}

export interface TattooRequest {
  id: number
  user_id: number
  keywords: string[]
  description?: string
  personal_meaning?: string
  recommended_characters?: any
  recommended_idioms?: any
  ai_explanation?: string
  selected_type?: 'character' | 'idiom'
  selected_content?: string
  preferred_style?: string
  preferred_position?: string
  size_preference?: string
  status: string
  is_paid: boolean
  created_at: string
  updated_at?: string
}

export interface TattooImage {
  id: number
  request_id: number
  image_url: string
  thumbnail_url?: string
  watermarked_url?: string
  style: string
  position: string
  content: string
  width?: number
  height?: number
  file_size?: number
  generation_prompt?: string
  generation_model?: string
  generation_time?: number
  is_high_resolution: boolean
  is_watermarked: boolean
  quality_score?: number
  created_at: string
}

export interface TattooStyle {
  id: number
  name: string
  name_zh: string
  description?: string
  characteristics?: any
  suitable_positions?: string[]
  difficulty_level: number
  example_images?: string[]
  prompt_template?: string
  is_active: boolean
  is_premium: boolean
  created_at: string
}

export interface ApiResponse<T> {
  data?: T
  message?: string
  error?: string
}

export interface SearchResponse {
  characters: ChineseCharacter[]
  idioms: ChineseIdiom[]
  total: number
}
