#!/usr/bin/env python3
"""
ASCII-only database initialization script
Avoids all encoding issues by using only ASCII characters
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError

def create_database():
    """Create database"""
    try:
        print("Connecting to PostgreSQL...")
        
        # Simple connection string
        engine = create_engine(
            "postgresql://postgres:password123@localhost:5432/postgres",
            isolation_level="AUTOCOMMIT"
        )
        
        with engine.connect() as conn:
            # Check if database exists
            result = conn.execute(text(
                "SELECT 1 FROM pg_database WHERE datname = 'chinese_tattoo_ai'"
            ))
            
            if not result.fetchone():
                # Create database with UTF8 encoding
                conn.execute(text(
                    "CREATE DATABASE chinese_tattoo_ai "
                    "WITH ENCODING 'UTF8' "
                    "LC_COLLATE='C' "
                    "LC_CTYPE='C' "
                    "TEMPLATE=template0"
                ))
                print("SUCCESS: Database 'chinese_tattoo_ai' created")
            else:
                print("SUCCESS: Database 'chinese_tattoo_ai' already exists")
                
    except OperationalError as e:
        error_msg = str(e)
        print(f"ERROR: Database connection failed: {error_msg}")
        
        if "password authentication failed" in error_msg:
            print("SOLUTION: Check if PostgreSQL password is 'password123'")
        elif "could not connect to server" in error_msg:
            print("SOLUTION: Make sure PostgreSQL service is running")
        elif "database" in error_msg and "does not exist" in error_msg:
            print("SOLUTION: Make sure PostgreSQL is properly installed")
        
        return False
    except Exception as e:
        print(f"ERROR: Failed to create database: {e}")
        return False
    
    return True

def create_tables():
    """Create database tables"""
    try:
        print("Creating database tables...")
        
        # Connect to target database
        engine = create_engine(
            "postgresql://postgres:password123@localhost:5432/chinese_tattoo_ai"
        )
        
        with engine.connect() as conn:
            # Create users table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS users (
                    id SERIAL PRIMARY KEY,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    username VARCHAR(100) UNIQUE NOT NULL,
                    hashed_password VARCHAR(255) NOT NULL,
                    full_name VARCHAR(255),
                    is_active BOOLEAN DEFAULT TRUE,
                    is_verified BOOLEAN DEFAULT FALSE,
                    is_premium BOOLEAN DEFAULT FALSE,
                    credits INTEGER DEFAULT 10,
                    avatar_url VARCHAR(500),
                    bio TEXT,
                    language_preference VARCHAR(10) DEFAULT 'zh',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            """))
            
            # Create chinese_characters table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS chinese_characters (
                    id SERIAL PRIMARY KEY,
                    character VARCHAR(10) UNIQUE NOT NULL,
                    pinyin VARCHAR(50) NOT NULL,
                    meaning_en TEXT NOT NULL,
                    meaning_zh TEXT NOT NULL,
                    cultural_context TEXT,
                    category VARCHAR(50),
                    tags TEXT[],
                    tattoo_popularity INTEGER DEFAULT 0,
                    recommended_positions TEXT[],
                    is_active BOOLEAN DEFAULT TRUE,
                    is_verified BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # Create chinese_idioms table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS chinese_idioms (
                    id SERIAL PRIMARY KEY,
                    idiom VARCHAR(50) UNIQUE NOT NULL,
                    pinyin VARCHAR(200) NOT NULL,
                    meaning_en TEXT NOT NULL,
                    meaning_zh TEXT NOT NULL,
                    origin_story TEXT,
                    cultural_context TEXT,
                    category VARCHAR(50),
                    tags TEXT[],
                    character_count INTEGER NOT NULL,
                    difficulty_level INTEGER DEFAULT 1,
                    tattoo_popularity INTEGER DEFAULT 0,
                    recommended_positions TEXT[],
                    is_active BOOLEAN DEFAULT TRUE,
                    is_verified BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # Create tattoo_requests table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS tattoo_requests (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id),
                    keywords TEXT[],
                    description TEXT,
                    personal_meaning TEXT,
                    recommended_characters JSONB,
                    recommended_idioms JSONB,
                    ai_explanation TEXT,
                    selected_type VARCHAR(20),
                    selected_content VARCHAR(100),
                    preferred_style VARCHAR(50),
                    preferred_position VARCHAR(50),
                    size_preference VARCHAR(20),
                    status VARCHAR(20) DEFAULT 'pending',
                    is_paid BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # Create tattoo_images table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS tattoo_images (
                    id SERIAL PRIMARY KEY,
                    request_id INTEGER REFERENCES tattoo_requests(id),
                    image_url VARCHAR(500) NOT NULL,
                    thumbnail_url VARCHAR(500),
                    watermarked_url VARCHAR(500),
                    style VARCHAR(50) NOT NULL,
                    position VARCHAR(50) NOT NULL,
                    content VARCHAR(100) NOT NULL,
                    width INTEGER,
                    height INTEGER,
                    file_size INTEGER,
                    generation_prompt TEXT,
                    generation_model VARCHAR(50),
                    generation_time FLOAT,
                    is_high_resolution BOOLEAN DEFAULT FALSE,
                    is_watermarked BOOLEAN DEFAULT TRUE,
                    quality_score FLOAT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # Create tattoo_styles table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS tattoo_styles (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(50) UNIQUE NOT NULL,
                    name_zh VARCHAR(50) NOT NULL,
                    description TEXT,
                    characteristics JSONB,
                    suitable_positions TEXT[],
                    difficulty_level INTEGER DEFAULT 1,
                    example_images TEXT[],
                    prompt_template TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_premium BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            conn.commit()
            print("SUCCESS: Database tables created")
            
    except Exception as e:
        print(f"ERROR: Failed to create tables: {e}")
        return False
    
    return True

def insert_sample_data():
    """Insert sample data using only ASCII characters"""
    try:
        print("Inserting sample data...")
        
        engine = create_engine(
            "postgresql://postgres:password123@localhost:5432/chinese_tattoo_ai"
        )
        
        with engine.connect() as conn:
            # Insert tattoo styles (ASCII only)
            conn.execute(text("""
                INSERT INTO tattoo_styles (name, name_zh, description, difficulty_level)
                VALUES 
                ('calligraphy', 'Calligraphy', 'Traditional Chinese calligraphy style', 3),
                ('modern', 'Modern', 'Clean and modern design style', 2),
                ('watercolor', 'Watercolor', 'Soft watercolor painting effect', 4),
                ('tribal', 'Tribal', 'Bold tribal pattern design', 3)
                ON CONFLICT (name) DO NOTHING
            """))
            
            # Insert basic character data (ASCII descriptions)
            conn.execute(text("""
                INSERT INTO chinese_characters (character, pinyin, meaning_en, meaning_zh, cultural_context, tattoo_popularity)
                VALUES 
                ('love', 'ai', 'love, affection', 'love and care', 'Symbol of love and compassion', 95),
                ('wisdom', 'zhi', 'wisdom, intelligence', 'wisdom and knowledge', 'Symbol of wisdom and intelligence', 88),
                ('courage', 'yong', 'courage, bravery', 'courage and bravery', 'Symbol of courage and strength', 92),
                ('harmony', 'he', 'harmony, peace', 'harmony and peace', 'Symbol of harmony and balance', 85),
                ('fortune', 'fu', 'fortune, blessing', 'fortune and happiness', 'Symbol of good fortune', 98)
                ON CONFLICT (character) DO NOTHING
            """))
            
            conn.commit()
            print("SUCCESS: Sample data inserted")
            
    except Exception as e:
        print(f"ERROR: Failed to insert sample data: {e}")
        return False
    
    return True

def main():
    """Main function"""
    print("=== Chinese Character Tattoo AI Database Initialization ===")
    
    # Create database
    if not create_database():
        print("\nPlease check the following:")
        print("1. PostgreSQL service is running")
        print("2. Username is 'postgres'")
        print("3. Password is 'password123'")
        print("4. Port 5432 is accessible")
        sys.exit(1)
    
    # Create tables
    if not create_tables():
        sys.exit(1)
    
    # Insert sample data
    if not insert_sample_data():
        sys.exit(1)
    
    print("\nSUCCESS: Database initialization completed!")
    print("You can now start the backend service:")
    print("uvicorn main:app --reload --host 0.0.0.0 --port 8000")

if __name__ == "__main__":
    main()
