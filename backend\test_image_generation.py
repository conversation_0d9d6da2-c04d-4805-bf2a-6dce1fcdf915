#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图像生成服务
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.image_generation_service import ImageGenerationService


async def test_image_generation():
    """测试图像生成服务"""

    print("🔍 开始测试图像生成服务...")

    # 创建图像生成服务实例
    image_service = ImageGenerationService()

    # 测试提示词
    test_prompts = ["智 - 智慧的汉字纹身设计", "勇 - 勇气的汉字纹身设计", "自强不息 - 成语纹身设计"]

    # 只测试第一个提示词，避免并发限制
    prompt = test_prompts[0]
    print(f"\n📝 测试: {prompt}")

    try:
        # 测试标准分辨率
        print("🚀 生成标准分辨率图片...")
        result = await image_service.generate_tattoo_image(prompt=prompt, high_resolution=False)

        print("✅ 图像生成成功！")
        print(f"   图片URL: {result.get('image_url', '')}")
        print(f"   缩略图URL: {result.get('thumbnail_url', '')}")
        print(f"   尺寸: {result.get('width', 0)}x{result.get('height', 0)}")
        print(f"   文件大小: {result.get('file_size', 0)} bytes")
        print(f"   模型: {result.get('model', '')}")
        print(f"   质量评分: {result.get('quality_score', 0)}")
        print(f"   是否有水印: {result.get('is_watermarked', False)}")

    except Exception as e:
        print(f"❌ 图像生成失败: {e}")
        print("🔧 可能的原因:")
        print("   1. API密钥配置错误")
        print("   2. 网络连接问题")
        print("   3. API服务不可用")
        print("   4. 提示词格式问题")


async def test_high_resolution():
    """测试高分辨率图像生成"""

    print("\n🔍 测试高分辨率图像生成...")

    image_service = ImageGenerationService()
    prompt = "福 - 福气的汉字纹身设计，高分辨率"

    try:
        print("🚀 生成高分辨率图片...")
        result = await image_service.generate_tattoo_image(prompt=prompt, high_resolution=True)

        print("✅ 高分辨率图像生成成功！")
        print(f"   图片URL: {result.get('image_url', '')}")
        print(f"   尺寸: {result.get('width', 0)}x{result.get('height', 0)}")
        print(f"   文件大小: {result.get('file_size', 0)} bytes")
        print(f"   是否有水印: {result.get('is_watermarked', False)}")

    except Exception as e:
        print(f"❌ 高分辨率图像生成失败: {e}")


async def test_prompt_building():
    """测试提示词构建"""

    print("\n🔍 测试提示词构建...")

    image_service = ImageGenerationService()

    test_cases = [("智", False), ("勇", True), ("自强不息", False)]

    for original_prompt, high_res in test_cases:
        built_prompt = image_service._build_tattoo_prompt(original_prompt, high_res)
        print(f"\n📝 原始提示词: {original_prompt}")
        print(f"🔧 高分辨率: {high_res}")
        print(f"🎯 构建的提示词: {built_prompt}")


if __name__ == "__main__":
    print("🎨 图像生成服务测试")
    print("=" * 50)

    # 只运行基础测试，避免并发限制
    asyncio.run(test_image_generation())

    print("\n🎊 测试完成！")
