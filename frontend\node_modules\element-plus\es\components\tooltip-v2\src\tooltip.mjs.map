{"version": 3, "file": "tooltip.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/tooltip.vue"], "sourcesContent": ["<template>\n  <tooltip-v2-root v-bind=\"rootProps\">\n    <template #default=\"{ open }\">\n      <tooltip-v2-trigger v-bind=\"triggerProps\" nowrap>\n        <slot name=\"trigger\" />\n      </tooltip-v2-trigger>\n      <el-teleport :to=\"to\" :disabled=\"!teleported\">\n        <template v-if=\"fullTransition\">\n          <transition v-bind=\"transitionProps\">\n            <tooltip-v2-content v-if=\"alwaysOn || open\" v-bind=\"contentProps\">\n              <slot />\n              <template #arrow=\"{ style, side }\">\n                <tooltip-v2-arrow\n                  v-if=\"showArrow\"\n                  v-bind=\"arrowProps\"\n                  :style=\"style\"\n                  :side=\"side\"\n                />\n              </template>\n            </tooltip-v2-content>\n          </transition>\n        </template>\n        <template v-else>\n          <tooltip-v2-content v-if=\"alwaysOn || open\" v-bind=\"contentProps\">\n            <slot />\n            <template #arrow=\"{ style, side }\">\n              <tooltip-v2-arrow\n                v-if=\"showArrow\"\n                v-bind=\"arrowProps\"\n                :style=\"style\"\n                :side=\"side\"\n              />\n            </template>\n          </tooltip-v2-content>\n        </template>\n      </el-teleport>\n    </template>\n  </tooltip-v2-root>\n</template>\n\n<script setup lang=\"ts\">\n// @ts-nocheck\nimport { reactive, toRefs } from 'vue'\nimport { pick } from 'lodash-unified'\nimport ElTeleport from '@element-plus/components/teleport'\nimport { tooltipV2ArrowProps } from './arrow'\nimport { tooltipV2ContentProps } from './content'\nimport { tooltipV2RootProps } from './root'\nimport { tooltipV2Props } from './tooltip'\nimport { tooltipV2TriggerProps } from './trigger'\nimport TooltipV2Root from './root.vue'\nimport TooltipV2Arrow from './arrow.vue'\nimport TooltipV2Content from './content.vue'\nimport TooltipV2Trigger from './trigger.vue'\n\ndefineOptions({\n  name: 'ElTooltipV2',\n})\n\nconst props = defineProps(tooltipV2Props)\n\nconst refedProps = toRefs(props)\n\nconst arrowProps = reactive(pick(refedProps, Object.keys(tooltipV2ArrowProps)))\n\nconst contentProps = reactive(\n  pick(refedProps, Object.keys(tooltipV2ContentProps))\n)\n\nconst rootProps = reactive(pick(refedProps, Object.keys(tooltipV2RootProps)))\n\nconst triggerProps = reactive(\n  pick(refedProps, Object.keys(tooltipV2TriggerProps))\n)\n</script>\n"], "names": ["_openBlock", "_createBlock", "_normalizeProps", "_guardReactiveProps", "_withCtx", "_createVNode", "_mergeProps"], "mappings": ";;;;;;;;;;;;;;mCAuDc,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,UAAA,GAAa,OAAO,KAAK,CAAA,CAAA;AAE/B,IAAM,MAAA,UAAA,GAAa,SAAS,IAAK,CAAA,UAAA,EAAY,OAAO,IAAK,CAAA,mBAAmB,CAAC,CAAC,CAAA,CAAA;AAE9E,IAAA,MAAM,YAAe,GAAA,QAAA,CAAA,IAAA,CAAA,UAAA,EAAA,MAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IAAA,MACd,SAAA,GAAA,QAAmB,CAAA,IAAK,wBAAsB,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA;AAAA,IACrD,MAAA,YAAA,GAAA,QAAA,CAAA,IAAA,CAAA,UAAA,EAAA,MAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,CAAA;AAEA,IAAM,OAAA,CAAA,IAAA,EAAA;AAEN,MAAA,OAAqBA,SAAA,EAAA,EAAAC,WAAA,CAAA,aAAA,EAAAC,cAAA,CAAAC,kBAAA,CAAA,SAAA,CAAA,CAAA,EAAA;AAAA,QACd,OAAA,EAAAC,OAAmB,CAAA,CAAA,EAAA,IAAA,EAAK;AAAsB,UACrDC,WAAA,CAAA,gBAAA,EAAAC,UAAA,CAAA,YAAA,EAAA,EAAA,MAAA,EAAA,EAAA,EAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}