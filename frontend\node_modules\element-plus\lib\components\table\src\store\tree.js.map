{"version": 3, "file": "tree.js", "sources": ["../../../../../../../packages/components/table/src/store/tree.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, getCurrentInstance, ref, unref, watch } from 'vue'\nimport { isArray, isUndefined } from '@element-plus/utils'\nimport { getRowIdentity, walkTreeNode } from '../util'\n\nimport type { WatcherPropsData } from '.'\nimport type { Table, TableProps } from '../table/defaults'\n\nfunction useTree<T>(watcherData: WatcherPropsData<T>) {\n  const expandRowKeys = ref<string[]>([])\n  const treeData = ref<unknown>({})\n  const indent = ref(16)\n  const lazy = ref(false)\n  const lazyTreeNodeMap = ref({})\n  const lazyColumnIdentifier = ref('hasChildren')\n  const childrenColumnName = ref('children')\n  const checkStrictly = ref(false)\n  const instance = getCurrentInstance() as Table<T>\n  const normalizedData = computed(() => {\n    if (!watcherData.rowKey.value) return {}\n    const data = watcherData.data.value || []\n    return normalize(data)\n  })\n  const normalizedLazyNode = computed(() => {\n    const rowKey = watcherData.rowKey.value\n    const keys = Object.keys(lazyTreeNodeMap.value)\n    const res = {}\n    if (!keys.length) return res\n    keys.forEach((key) => {\n      if (lazyTreeNodeMap.value[key].length) {\n        const item = { children: [] }\n        lazyTreeNodeMap.value[key].forEach((row) => {\n          const currentRowKey = getRowIdentity(row, rowKey)\n          item.children.push(currentRowKey)\n          if (row[lazyColumnIdentifier.value] && !res[currentRowKey]) {\n            res[currentRowKey] = { children: [] }\n          }\n        })\n        res[key] = item\n      }\n    })\n    return res\n  })\n\n  const normalize = (data) => {\n    const rowKey = watcherData.rowKey.value\n    const res = {}\n    walkTreeNode(\n      data,\n      (parent, children, level) => {\n        const parentId = getRowIdentity(parent, rowKey)\n        if (isArray(children)) {\n          res[parentId] = {\n            children: children.map((row) => getRowIdentity(row, rowKey)),\n            level,\n          }\n        } else if (lazy.value) {\n          // 当 children 不存在且 lazy 为 true，该节点即为懒加载的节点\n          res[parentId] = {\n            children: [],\n            lazy: true,\n            level,\n          }\n        }\n      },\n      childrenColumnName.value,\n      lazyColumnIdentifier.value\n    )\n    return res\n  }\n\n  const updateTreeData = (\n    ifChangeExpandRowKeys = false,\n    ifExpandAll = instance.store?.states.defaultExpandAll.value\n  ) => {\n    const nested = normalizedData.value\n    const normalizedLazyNode_ = normalizedLazyNode.value\n    const keys = Object.keys(nested)\n    const newTreeData = {}\n\n    if (keys.length) {\n      const oldTreeData = unref(treeData)\n      const rootLazyRowKeys = []\n      const getExpanded = (oldValue, key) => {\n        if (ifChangeExpandRowKeys) {\n          if (expandRowKeys.value) {\n            return ifExpandAll || expandRowKeys.value.includes(key)\n          } else {\n            return !!(ifExpandAll || oldValue?.expanded)\n          }\n        } else {\n          const included =\n            ifExpandAll ||\n            (expandRowKeys.value && expandRowKeys.value.includes(key))\n          return !!(oldValue?.expanded || included)\n        }\n      }\n      // 合并 expanded 与 display，确保数据刷新后，状态不变\n      keys.forEach((key) => {\n        const oldValue = oldTreeData[key]\n        const newValue = { ...nested[key] }\n        newValue.expanded = getExpanded(oldValue, key)\n        if (newValue.lazy) {\n          const { loaded = false, loading = false } = oldValue || {}\n          newValue.loaded = !!loaded\n          newValue.loading = !!loading\n          rootLazyRowKeys.push(key)\n        }\n        newTreeData[key] = newValue\n      })\n      // 根据懒加载数据更新 treeData\n      const lazyKeys = Object.keys(normalizedLazyNode_)\n      if (lazy.value && lazyKeys.length && rootLazyRowKeys.length) {\n        lazyKeys.forEach((key) => {\n          const oldValue = oldTreeData[key]\n          const lazyNodeChildren = normalizedLazyNode_[key].children\n          if (rootLazyRowKeys.includes(key)) {\n            // 懒加载的 root 节点，更新一下原有的数据，原来的 children 一定是空数组\n            if (newTreeData[key].children.length !== 0) {\n              throw new Error('[ElTable]children must be an empty array.')\n            }\n            newTreeData[key].children = lazyNodeChildren\n          } else {\n            const { loaded = false, loading = false } = oldValue || {}\n            newTreeData[key] = {\n              lazy: true,\n              loaded: !!loaded,\n              loading: !!loading,\n              expanded: getExpanded(oldValue, key),\n              children: lazyNodeChildren,\n              level: '',\n            }\n          }\n        })\n      }\n    }\n    treeData.value = newTreeData\n    instance.store?.updateTableScrollY()\n  }\n\n  watch(\n    () => expandRowKeys.value,\n    () => {\n      updateTreeData(true)\n    }\n  )\n\n  watch(\n    () => normalizedData.value,\n    () => {\n      updateTreeData()\n    }\n  )\n  watch(\n    () => normalizedLazyNode.value,\n    () => {\n      updateTreeData()\n    }\n  )\n\n  const updateTreeExpandKeys = (value: string[]) => {\n    expandRowKeys.value = value\n    updateTreeData()\n  }\n  const isUseLazy = (data): boolean => {\n    return lazy.value && data && 'loaded' in data && !data.loaded\n  }\n  const toggleTreeExpansion = (row: T, expanded?: boolean) => {\n    instance.store.assertRowKey()\n\n    const rowKey = watcherData.rowKey.value\n    const id = getRowIdentity(row, rowKey)\n    const data = id && treeData.value[id]\n    if (id && data && 'expanded' in data) {\n      const oldExpanded = data.expanded\n      expanded = isUndefined(expanded) ? !data.expanded : expanded\n      treeData.value[id].expanded = expanded\n      if (oldExpanded !== expanded) {\n        instance.emit('expand-change', row, expanded)\n      }\n      isUseLazy(data) && loadData(row, id, data)\n      instance.store.updateTableScrollY()\n    }\n  }\n\n  const loadOrToggle = (row) => {\n    instance.store.assertRowKey()\n    const rowKey = watcherData.rowKey.value\n    const id = getRowIdentity(row, rowKey)\n    const data = treeData.value[id]\n    if (isUseLazy(data)) {\n      loadData(row, id, data)\n    } else {\n      toggleTreeExpansion(row, undefined)\n    }\n  }\n\n  const loadData = (row: T, key: string, treeNode) => {\n    const { load } = instance.props as unknown as TableProps<T>\n    if (load && !treeData.value[key].loaded) {\n      treeData.value[key].loading = true\n      load(row, treeNode, (data) => {\n        if (!isArray(data)) {\n          throw new TypeError('[ElTable] data must be an array')\n        }\n        treeData.value[key].loading = false\n        treeData.value[key].loaded = true\n        treeData.value[key].expanded = true\n        if (data.length) {\n          lazyTreeNodeMap.value[key] = data\n        }\n        instance.emit('expand-change', row, true)\n      })\n    }\n  }\n\n  const updateKeyChildren = (key: string, data: T[]) => {\n    const { lazy, rowKey } = instance.props as unknown as TableProps<T>\n    if (!lazy) return\n    if (!rowKey) throw new Error('[Table] rowKey is required in updateKeyChild')\n\n    if (lazyTreeNodeMap.value[key]) {\n      lazyTreeNodeMap.value[key] = data\n    }\n  }\n\n  return {\n    loadData,\n    loadOrToggle,\n    toggleTreeExpansion,\n    updateTreeExpandKeys,\n    updateTreeData,\n    updateKeyChildren,\n    normalize,\n    states: {\n      expandRowKeys,\n      treeData,\n      indent,\n      lazy,\n      lazyTreeNodeMap,\n      lazyColumnIdentifier,\n      childrenColumnName,\n      checkStrictly,\n    },\n  }\n}\n\nexport default useTree\n"], "names": ["ref", "getCurrentInstance", "computed", "getRowIdentity", "walkTreeNode", "isArray", "unref", "watch", "isUndefined"], "mappings": ";;;;;;;;;AAGA,SAAS,OAAO,CAAC,WAAW,EAAE;AAC9B,EAAE,MAAM,aAAa,GAAGA,OAAG,CAAC,EAAE,CAAC,CAAC;AAChC,EAAE,MAAM,QAAQ,GAAGA,OAAG,CAAC,EAAE,CAAC,CAAC;AAC3B,EAAE,MAAM,MAAM,GAAGA,OAAG,CAAC,EAAE,CAAC,CAAC;AACzB,EAAE,MAAM,IAAI,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,MAAM,eAAe,GAAGA,OAAG,CAAC,EAAE,CAAC,CAAC;AAClC,EAAE,MAAM,oBAAoB,GAAGA,OAAG,CAAC,aAAa,CAAC,CAAC;AAClD,EAAE,MAAM,kBAAkB,GAAGA,OAAG,CAAC,UAAU,CAAC,CAAC;AAC7C,EAAE,MAAM,aAAa,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AACnC,EAAE,MAAM,QAAQ,GAAGC,sBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,cAAc,GAAGC,YAAQ,CAAC,MAAM;AACxC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK;AACjC,MAAM,OAAO,EAAE,CAAC;AAChB,IAAI,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;AAC9C,IAAI,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;AAC3B,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAGA,YAAQ,CAAC,MAAM;AAC5C,IAAI,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;AAC5C,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACpD,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;AACpB,MAAM,OAAO,GAAG,CAAC;AACjB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC1B,MAAM,IAAI,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE;AAC7C,QAAQ,MAAM,IAAI,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AACtC,QAAQ,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AACpD,UAAU,MAAM,aAAa,GAAGC,mBAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC5D,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC5C,UAAU,IAAI,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;AACtE,YAAY,GAAG,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AAClD,WAAW;AACX,SAAS,CAAC,CAAC;AACX,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACxB,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,GAAG,CAAC;AACf,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,SAAS,GAAG,CAAC,IAAI,KAAK;AAC9B,IAAI,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;AAC5C,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAIC,iBAAY,CAAC,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,KAAK;AACpD,MAAM,MAAM,QAAQ,GAAGD,mBAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACtD,MAAM,IAAIE,cAAO,CAAC,QAAQ,CAAC,EAAE;AAC7B,QAAQ,GAAG,CAAC,QAAQ,CAAC,GAAG;AACxB,UAAU,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,KAAKF,mBAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACtE,UAAU,KAAK;AACf,SAAS,CAAC;AACV,OAAO,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE;AAC7B,QAAQ,GAAG,CAAC,QAAQ,CAAC,GAAG;AACxB,UAAU,QAAQ,EAAE,EAAE;AACtB,UAAU,IAAI,EAAE,IAAI;AACpB,UAAU,KAAK;AACf,SAAS,CAAC;AACV,OAAO;AACP,KAAK,EAAE,kBAAkB,CAAC,KAAK,EAAE,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAC7D,IAAI,OAAO,GAAG,CAAC;AACf,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,qBAAqB,GAAG,KAAK,EAAE,WAAW,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,GAAG,KAAK;AACjK,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,MAAM,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC;AACxC,IAAI,MAAM,mBAAmB,GAAG,kBAAkB,CAAC,KAAK,CAAC;AACzD,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC;AAC3B,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AACrB,MAAM,MAAM,WAAW,GAAGG,SAAK,CAAC,QAAQ,CAAC,CAAC;AAC1C,MAAM,MAAM,eAAe,GAAG,EAAE,CAAC;AACjC,MAAM,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE,GAAG,KAAK;AAC7C,QAAQ,IAAI,qBAAqB,EAAE;AACnC,UAAU,IAAI,aAAa,CAAC,KAAK,EAAE;AACnC,YAAY,OAAO,WAAW,IAAI,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACpE,WAAW,MAAM;AACjB,YAAY,OAAO,CAAC,EAAE,WAAW,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;AACtF,WAAW;AACX,SAAS,MAAM;AACf,UAAU,MAAM,QAAQ,GAAG,WAAW,IAAI,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACnG,UAAU,OAAO,CAAC,EAAE,CAAC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;AACjF,SAAS;AACT,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC5B,QAAQ,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAC1C,QAAQ,MAAM,QAAQ,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;AAC5C,QAAQ,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AACvD,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC3B,UAAU,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,QAAQ,IAAI,EAAE,CAAC;AACrE,UAAU,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;AACrC,UAAU,QAAQ,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC;AACvC,UAAU,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpC,SAAS;AACT,QAAQ,WAAW,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;AACpC,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AACxD,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,MAAM,IAAI,eAAe,CAAC,MAAM,EAAE;AACnE,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAClC,UAAU,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;AAC5C,UAAU,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;AACrE,UAAU,IAAI,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC7C,YAAY,IAAI,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AACxD,cAAc,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;AAC3E,aAAa;AACb,YAAY,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,gBAAgB,CAAC;AACzD,WAAW,MAAM;AACjB,YAAY,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,QAAQ,IAAI,EAAE,CAAC;AACvE,YAAY,WAAW,CAAC,GAAG,CAAC,GAAG;AAC/B,cAAc,IAAI,EAAE,IAAI;AACxB,cAAc,MAAM,EAAE,CAAC,CAAC,MAAM;AAC9B,cAAc,OAAO,EAAE,CAAC,CAAC,OAAO;AAChC,cAAc,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE,GAAG,CAAC;AAClD,cAAc,QAAQ,EAAE,gBAAgB;AACxC,cAAc,KAAK,EAAE,EAAE;AACvB,aAAa,CAAC;AACd,WAAW;AACX,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK;AACL,IAAI,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC;AACjC,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,kBAAkB,EAAE,CAAC;AACvE,GAAG,CAAC;AACJ,EAAEC,SAAK,CAAC,MAAM,aAAa,CAAC,KAAK,EAAE,MAAM;AACzC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;AACzB,GAAG,CAAC,CAAC;AACL,EAAEA,SAAK,CAAC,MAAM,cAAc,CAAC,KAAK,EAAE,MAAM;AAC1C,IAAI,cAAc,EAAE,CAAC;AACrB,GAAG,CAAC,CAAC;AACL,EAAEA,SAAK,CAAC,MAAM,kBAAkB,CAAC,KAAK,EAAE,MAAM;AAC9C,IAAI,cAAc,EAAE,CAAC;AACrB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,oBAAoB,GAAG,CAAC,KAAK,KAAK;AAC1C,IAAI,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;AAChC,IAAI,cAAc,EAAE,CAAC;AACrB,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,CAAC,IAAI,KAAK;AAC9B,IAAI,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAClE,GAAG,CAAC;AACJ,EAAE,MAAM,mBAAmB,GAAG,CAAC,GAAG,EAAE,QAAQ,KAAK;AACjD,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;AAClC,IAAI,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;AAC5C,IAAI,MAAM,EAAE,GAAGJ,mBAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,MAAM,IAAI,GAAG,EAAE,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC1C,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,UAAU,IAAI,IAAI,EAAE;AAC1C,MAAM,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC;AACxC,MAAM,QAAQ,GAAGK,iBAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACnE,MAAM,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7C,MAAM,IAAI,WAAW,KAAK,QAAQ,EAAE;AACpC,QAAQ,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;AACtD,OAAO;AACP,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AACjD,MAAM,QAAQ,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;AAC1C,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,CAAC,GAAG,KAAK;AAChC,IAAI,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;AAClC,IAAI,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;AAC5C,IAAI,MAAM,EAAE,GAAGL,mBAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACpC,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;AACzB,MAAM,QAAQ,CAAC,GAAG,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;AAC9B,KAAK,MAAM;AACX,MAAM,mBAAmB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AACvC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,KAAK;AAC3C,IAAI,MAAM,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC;AACpC,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE;AAC7C,MAAM,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;AACzC,MAAM,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,IAAI,KAAK;AACpC,QAAQ,IAAI,CAACE,cAAO,CAAC,IAAI,CAAC,EAAE;AAC5B,UAAU,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;AACjE,SAAS;AACT,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC;AAC5C,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;AAC1C,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC5C,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE;AACzB,UAAU,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC5C,SAAS;AACT,QAAQ,QAAQ,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AAClD,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK;AAC3C,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC;AACnD,IAAI,IAAI,CAAC,KAAK;AACd,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,MAAM;AACf,MAAM,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;AACtE,IAAI,IAAI,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACpC,MAAM,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACxC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI,mBAAmB;AACvB,IAAI,oBAAoB;AACxB,IAAI,cAAc;AAClB,IAAI,iBAAiB;AACrB,IAAI,SAAS;AACb,IAAI,MAAM,EAAE;AACZ,MAAM,aAAa;AACnB,MAAM,QAAQ;AACd,MAAM,MAAM;AACZ,MAAM,IAAI;AACV,MAAM,eAAe;AACrB,MAAM,oBAAoB;AAC1B,MAAM,kBAAkB;AACxB,MAAM,aAAa;AACnB,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}