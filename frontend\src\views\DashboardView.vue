<template>
  <div class="dashboard-page">
    <!-- 页面头部 -->
    <section class="dashboard-header">
      <div class="container">
        <div class="header-content">
          <div class="welcome-section">
            <h1>欢迎回来，{{ userStore.user?.full_name || userStore.user?.username }}！</h1>
            <p>管理您的纹身设计和账户信息</p>
          </div>
          <div class="user-stats">
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><Coin /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ userStore.user?.credits || 0 }}</div>
                <div class="stat-label">可用积分</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ totalDesigns }}</div>
                <div class="stat-label">设计总数</div>
              </div>
            </div>
            <div class="stat-card premium" v-if="userStore.user?.is_premium">
              <div class="stat-icon">
                <el-icon><Star /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">高级</div>
                <div class="stat-label">会员状态</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 快速操作 -->
    <section class="quick-actions">
      <div class="container">
        <h2>快速操作</h2>
        <div class="actions-grid">
          <div class="action-card" @click="$router.push('/recommend')">
            <div class="action-icon">
              <el-icon><MagicStick /></el-icon>
            </div>
            <h3>AI推荐</h3>
            <p>获取个性化的汉字推荐</p>
          </div>
          
          <div class="action-card" @click="$router.push('/search')">
            <div class="action-icon">
              <el-icon><Search /></el-icon>
            </div>
            <h3>搜索汉字</h3>
            <p>探索汉字的文化内涵</p>
          </div>
          
          <div class="action-card" @click="$router.push('/tattoo-design')">
            <div class="action-icon">
              <el-icon><Picture /></el-icon>
            </div>
            <h3>设计纹身</h3>
            <p>生成专属纹身设计</p>
          </div>
          
          <div class="action-card" @click="$router.push('/profile')">
            <div class="action-icon">
              <el-icon><User /></el-icon>
            </div>
            <h3>个人资料</h3>
            <p>管理账户和设置</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 最近的设计 -->
    <section class="recent-designs">
      <div class="container">
        <div class="section-header">
          <h2>最近的设计</h2>
          <el-button type="text" @click="$router.push('/profile?tab=designs')">
            查看全部
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </div>
        
        <div v-if="recentDesigns.length > 0" class="designs-grid">
          <div 
            v-for="design in recentDesigns" 
            :key="design.id"
            class="design-card"
            @click="viewDesignDetail(design)"
          >
            <div class="design-image">
              <img :src="design.thumbnail_url || design.image_url" :alt="design.content" />
              <div class="design-overlay">
                <el-button type="primary" size="small" circle>
                  <el-icon><View /></el-icon>
                </el-button>
              </div>
            </div>
            <div class="design-info">
              <div class="design-content">{{ design.content }}</div>
              <div class="design-meta">
                <span class="design-style">{{ design.style }}</span>
                <span class="design-date">{{ formatDate(design.created_at) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-designs">
          <el-icon size="64"><Picture /></el-icon>
          <h3>还没有设计</h3>
          <p>开始创建您的第一个纹身设计吧！</p>
          <el-button type="primary" @click="$router.push('/recommend')">
            开始设计
          </el-button>
        </div>
      </div>
    </section>

    <!-- 推荐内容 -->
    <section class="recommendations">
      <div class="container">
        <h2>为您推荐</h2>
        <div class="recommendations-grid">
          <!-- 热门汉字 -->
          <div class="recommendation-card">
            <h3>热门汉字</h3>
            <div class="characters-list">
              <div 
                v-for="char in popularCharacters" 
                :key="char.id"
                class="character-item"
                @click="exploreCharacter(char)"
              >
                <div class="character-main">{{ char.character }}</div>
                <div class="character-meaning">{{ char.meaning_en }}</div>
              </div>
            </div>
          </div>
          
          <!-- 设计技巧 -->
          <div class="recommendation-card">
            <h3>设计技巧</h3>
            <div class="tips-list">
              <div class="tip-item">
                <el-icon><InfoFilled /></el-icon>
                <span>选择合适的身体部位很重要</span>
              </div>
              <div class="tip-item">
                <el-icon><InfoFilled /></el-icon>
                <span>了解汉字的文化背景</span>
              </div>
              <div class="tip-item">
                <el-icon><InfoFilled /></el-icon>
                <span>考虑纹身的长期意义</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Coin, Picture, Star, MagicStick, Search, User, 
  ArrowRight, View, InfoFilled 
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/counter'
import { TattooService } from '@/services/tattoo'
import { CharacterService } from '@/services/character'
import type { TattooImage, ChineseCharacter } from '@/types'

const router = useRouter()
const userStore = useUserStore()

const totalDesigns = ref(0)
const recentDesigns = ref<TattooImage[]>([])
const popularCharacters = ref<ChineseCharacter[]>([])

onMounted(async () => {
  // 检查用户登录状态
  if (!userStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // 加载用户数据
  await loadUserData()
})

// 加载用户数据
const loadUserData = async () => {
  try {
    // 加载最近的设计
    const designs = await TattooService.getUserTattooHistory({ limit: 6 })
    recentDesigns.value = designs
    totalDesigns.value = designs.length

    // 加载热门汉字
    const popular = await CharacterService.getPopular({ type: 'characters', limit: 6 })
    popularCharacters.value = popular.characters || []

  } catch (error) {
    console.error('加载用户数据失败:', error)
  }
}

// 查看设计详情
const viewDesignDetail = (design: TattooImage) => {
  router.push({
    path: '/tattoo-design',
    query: {
      content: design.content,
      style: design.style,
      position: design.position
    }
  })
}

// 探索汉字
const exploreCharacter = (character: ChineseCharacter) => {
  router.push(`/search?q=${character.character}`)
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.dashboard-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 页面头部 */
.dashboard-header {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 30px;
}

.welcome-section h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
  font-family: 'SimSun', serif;
}

.welcome-section p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.user-stats {
  display: flex;
  gap: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px 20px;
  backdrop-filter: blur(10px);
  min-width: 120px;
}

.stat-card.premium {
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(255, 215, 0, 0.1) 100%);
  border-color: rgba(255, 215, 0, 0.3);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1a1a1a;
  font-size: 18px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  line-height: 1;
}

.stat-label {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}

/* 快速操作 */
.quick-actions {
  padding: 60px 0;
}

.quick-actions h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 30px 0;
  text-align: center;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.action-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 30px 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.action-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.action-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  color: #1a1a1a;
  font-size: 24px;
}

.action-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.action-card p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-size: 0.95rem;
}

/* 最近的设计 */
.recent-designs {
  padding: 60px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.section-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.designs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.design-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.design-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
}

.design-image {
  position: relative;
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.design-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.design-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.design-card:hover .design-overlay {
  opacity: 1;
}

.design-info {
  padding: 12px;
}

.design-content {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffd700;
  font-family: 'SimSun', serif;
  margin-bottom: 4px;
}

.design-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}

.empty-designs {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.empty-designs h3 {
  color: #ffffff;
  margin: 16px 0 8px 0;
  font-size: 1.5rem;
}

.empty-designs p {
  margin: 0 0 24px 0;
}

/* 推荐内容 */
.recommendations {
  padding: 60px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.recommendations h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 30px 0;
  text-align: center;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.recommendation-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
}

.recommendation-card h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffd700;
  margin: 0 0 20px 0;
}

.characters-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.character-item {
  text-align: center;
  padding: 12px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.character-item:hover {
  background: rgba(255, 215, 0, 0.1);
  transform: translateY(-2px);
}

.character-main {
  font-size: 2rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  margin-bottom: 4px;
}

.character-meaning {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.tip-item .el-icon {
  color: #ffd700;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .user-stats {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .stat-card {
    min-width: 100px;
  }
  
  .welcome-section h1 {
    font-size: 2rem;
  }
  
  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .designs-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .characters-list {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
