#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复tattoo_images表结构
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db.database import engine


def fix_tattoo_images_table():
    """修复tattoo_images表结构"""

    print("🔧 修复tattoo_images表结构...")

    with engine.connect() as conn:
        # 检查当前表结构
        print("📋 检查当前表结构...")
        result = conn.execute(text("DESCRIBE tattoo_images"))
        columns = [row[0] for row in result.fetchall()]
        print(f"当前字段: {columns}")

        # 添加缺失的字段
        missing_fields = []

        if "watermarked_url" not in columns:
            missing_fields.append("ADD COLUMN watermarked_url VARCHAR(500)")

        if "file_size" not in columns:
            missing_fields.append("ADD COLUMN file_size INT")

        if "generation_prompt" not in columns:
            missing_fields.append("ADD COLUMN generation_prompt TEXT")

        if "generation_model" not in columns:
            missing_fields.append("ADD COLUMN generation_model VARCHAR(50)")

        if missing_fields:
            print(f"🔨 添加缺失字段: {missing_fields}")

            for field in missing_fields:
                try:
                    sql = f"ALTER TABLE tattoo_images {field}"
                    print(f"执行: {sql}")
                    conn.execute(text(sql))
                    conn.commit()
                    print(f"✅ 成功添加字段")
                except Exception as e:
                    print(f"❌ 添加字段失败: {e}")
        else:
            print("✅ 表结构已经完整")

        # 再次检查表结构
        print("📋 修复后的表结构...")
        result = conn.execute(text("DESCRIBE tattoo_images"))
        for row in result.fetchall():
            print(f"  {row[0]}: {row[1]}")


if __name__ == "__main__":
    fix_tattoo_images_table()
