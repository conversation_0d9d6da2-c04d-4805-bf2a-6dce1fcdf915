"""
汉字成语相关数据模式
Character and Idiom Related Data Schemas
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

class CharacterBase(BaseModel):
    """汉字基础模式"""
    character: str
    pinyin: str
    meaning_en: str
    meaning_zh: str
    cultural_context: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None

class CharacterCreate(CharacterBase):
    """汉字创建模式"""
    usage_examples: Optional[Dict[str, Any]] = None
    recommended_positions: Optional[List[str]] = None

class CharacterResponse(CharacterBase):
    """汉字响应模式"""
    id: int
    usage_examples: Optional[Dict[str, Any]] = None
    tattoo_popularity: int
    recommended_positions: Optional[List[str]] = None
    is_active: bool
    is_verified: bool
    created_at: datetime

    class Config:
        from_attributes = True

class IdiomBase(BaseModel):
    """成语基础模式"""
    idiom: str
    pinyin: str
    meaning_en: str
    meaning_zh: str
    origin_story: Optional[str] = None
    cultural_context: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    character_count: int

class IdiomCreate(IdiomBase):
    """成语创建模式"""
    usage_examples: Optional[Dict[str, Any]] = None
    recommended_positions: Optional[List[str]] = None
    difficulty_level: int = 1

class IdiomResponse(IdiomBase):
    """成语响应模式"""
    id: int
    usage_examples: Optional[Dict[str, Any]] = None
    difficulty_level: int
    tattoo_popularity: int
    recommended_positions: Optional[List[str]] = None
    is_active: bool
    is_verified: bool
    created_at: datetime

    class Config:
        from_attributes = True

class RecommendationRequest(BaseModel):
    """推荐请求模式"""
    keywords: List[str]
    description: Optional[str] = None
    type: str = "both"  # "character", "idiom", "both"
    max_results: int = 5

class CharacterRecommendation(BaseModel):
    """汉字推荐模式"""
    character: str
    pinyin: str
    meaning_en: str
    meaning_zh: str
    cultural_context: Optional[str] = None
    score: float
    explanation: str

class IdiomRecommendation(BaseModel):
    """成语推荐模式"""
    idiom: str
    pinyin: str
    meaning_en: str
    meaning_zh: str
    origin_story: Optional[str] = None
    score: float
    explanation: str

class RecommendationResponse(BaseModel):
    """推荐响应模式"""
    characters: List[CharacterRecommendation] = []
    idioms: List[IdiomRecommendation] = []
    ai_explanation: str

class SearchResponse(BaseModel):
    """搜索响应模式"""
    characters: List[CharacterResponse] = []
    idioms: List[IdiomResponse] = []
    total: int
