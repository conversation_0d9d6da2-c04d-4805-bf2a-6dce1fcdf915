#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库初始化脚本
"""

import sys
import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError


def create_database():
    """创建MySQL数据库"""
    try:
        print("正在连接MySQL服务器...")

        # 连接到MySQL服务器（不指定数据库）
        connection = pymysql.connect(host="localhost", user="root", password="123456", charset="utf8mb4")

        with connection.cursor() as cursor:
            # 检查数据库是否存在
            cursor.execute("SHOW DATABASES LIKE 'chinese_tattoo_ai'")
            result = cursor.fetchone()

            if not result:
                # 创建数据库
                cursor.execute(
                    """
                    CREATE DATABASE chinese_tattoo_ai 
                    CHARACTER SET utf8mb4 
                    COLLATE utf8mb4_unicode_ci
                """
                )
                connection.commit()
                print("✓ 数据库 'chinese_tattoo_ai' 创建成功")
            else:
                print("✓ 数据库 'chinese_tattoo_ai' 已存在")

        connection.close()
        return True

    except pymysql.Error as e:
        print(f"❌ MySQL连接失败: {e}")
        print("请检查：")
        print("1. MySQL服务是否已启动")
        print("2. 用户名是否为 'root'")
        print("3. 密码是否为 '123456'")
        print("4. 端口3306是否可访问")
        return False
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False


def create_tables():
    """创建数据表"""
    try:
        print("正在创建数据表...")

        # 连接到目标数据库
        engine = create_engine(
            "mysql+pymysql://root:123456@localhost:3306/chinese_tattoo_ai?charset=utf8mb4", echo=False
        )

        with engine.connect() as conn:
            # 创建用户表
            conn.execute(
                text(
                    """
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    username VARCHAR(100) UNIQUE NOT NULL,
                    hashed_password VARCHAR(255) NOT NULL,
                    full_name VARCHAR(255),
                    is_active BOOLEAN DEFAULT TRUE,
                    is_verified BOOLEAN DEFAULT FALSE,
                    is_premium BOOLEAN DEFAULT FALSE,
                    credits INT DEFAULT 10,
                    avatar_url VARCHAR(500),
                    bio TEXT,
                    language_preference VARCHAR(10) DEFAULT 'zh',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP NULL
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
                )
            )

            # 创建汉字表
            conn.execute(
                text(
                    """
                CREATE TABLE IF NOT EXISTS chinese_characters (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    `character` VARCHAR(10) UNIQUE NOT NULL,
                    pinyin VARCHAR(50) NOT NULL,
                    meaning_en TEXT NOT NULL,
                    meaning_zh TEXT NOT NULL,
                    cultural_context TEXT,
                    category VARCHAR(50),
                    tags JSON,
                    tattoo_popularity INT DEFAULT 0,
                    recommended_positions JSON,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_verified BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
                )
            )

            # 创建成语表
            conn.execute(
                text(
                    """
                CREATE TABLE IF NOT EXISTS chinese_idioms (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    idiom VARCHAR(50) UNIQUE NOT NULL,
                    pinyin VARCHAR(200) NOT NULL,
                    meaning_en TEXT NOT NULL,
                    meaning_zh TEXT NOT NULL,
                    origin_story TEXT,
                    cultural_context TEXT,
                    category VARCHAR(50),
                    tags JSON,
                    character_count INT NOT NULL,
                    difficulty_level INT DEFAULT 1,
                    tattoo_popularity INT DEFAULT 0,
                    recommended_positions JSON,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_verified BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
                )
            )

            # 创建纹身请求表
            conn.execute(
                text(
                    """
                CREATE TABLE IF NOT EXISTS tattoo_requests (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT,
                    keywords JSON,
                    description TEXT,
                    personal_meaning TEXT,
                    recommended_characters JSON,
                    recommended_idioms JSON,
                    ai_explanation TEXT,
                    selected_type VARCHAR(20),
                    selected_content VARCHAR(100),
                    preferred_style VARCHAR(50),
                    preferred_position VARCHAR(50),
                    size_preference VARCHAR(20),
                    status VARCHAR(20) DEFAULT 'pending',
                    is_paid BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
                )
            )

            # 创建纹身图片表
            conn.execute(
                text(
                    """
                CREATE TABLE IF NOT EXISTS tattoo_images (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    request_id INT,
                    image_url VARCHAR(500) NOT NULL,
                    thumbnail_url VARCHAR(500),
                    watermarked_url VARCHAR(500),
                    style VARCHAR(50) NOT NULL,
                    position VARCHAR(50) NOT NULL,
                    content VARCHAR(100) NOT NULL,
                    width INT,
                    height INT,
                    file_size INT,
                    generation_prompt TEXT,
                    generation_model VARCHAR(50),
                    generation_time FLOAT,
                    is_high_resolution BOOLEAN DEFAULT FALSE,
                    is_watermarked BOOLEAN DEFAULT TRUE,
                    quality_score FLOAT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (request_id) REFERENCES tattoo_requests(id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
                )
            )

            # 创建纹身风格表
            conn.execute(
                text(
                    """
                CREATE TABLE IF NOT EXISTS tattoo_styles (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(50) UNIQUE NOT NULL,
                    name_zh VARCHAR(50) NOT NULL,
                    description TEXT,
                    characteristics JSON,
                    suitable_positions JSON,
                    difficulty_level INT DEFAULT 1,
                    example_images JSON,
                    prompt_template TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_premium BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
                )
            )

            conn.commit()
            print("✓ 数据表创建成功")

    except Exception as e:
        print(f"❌ 创建数据表失败: {e}")
        return False

    return True


def insert_sample_data():
    """插入示例数据"""
    try:
        print("正在插入示例数据...")

        engine = create_engine("mysql+pymysql://root:123456@localhost:3306/chinese_tattoo_ai?charset=utf8mb4")

        with engine.connect() as conn:
            # 插入纹身风格
            conn.execute(
                text(
                    """
                INSERT IGNORE INTO tattoo_styles (name, name_zh, description, difficulty_level)
                VALUES 
                ('calligraphy', '书法体', '传统中国书法风格，优雅而有力', 3),
                ('modern', '现代简约', '简洁现代的设计风格', 2),
                ('watercolor', '水彩风格', '柔和的水彩画效果', 4),
                ('tribal', '部落风格', '粗犷有力的部落图案', 3)
            """
                )
            )

            # 插入示例汉字
            conn.execute(
                text(
                    """
                INSERT IGNORE INTO chinese_characters (character, pinyin, meaning_en, meaning_zh, cultural_context, tattoo_popularity)
                VALUES 
                ('爱', 'ài', 'love, affection', '爱情，喜爱', '爱是人类最基本的情感之一，代表着关怀、温暖和奉献。', 95),
                ('智', 'zhì', 'wisdom, intelligence', '智慧，聪明', '智慧是人生的重要品质，代表着理性思考和明智决策的能力。', 88),
                ('勇', 'yǒng', 'courage, bravery', '勇敢，勇气', '勇气是面对困难和挑战时的坚定意志，是成功的重要品质。', 92),
                ('和', 'hé', 'harmony, peace', '和谐，和平', '和谐代表着平衡与统一，是中华文化中的重要理念。', 85),
                ('福', 'fú', 'fortune, blessing', '福气，幸福', '福是中国文化中最受欢迎的吉祥字，代表着好运和幸福。', 98)
            """
                )
            )

            # 插入示例成语
            conn.execute(
                text(
                    """
                INSERT IGNORE INTO chinese_idioms (idiom, pinyin, meaning_en, meaning_zh, origin_story, character_count, tattoo_popularity)
                VALUES 
                ('自强不息', 'zì qiáng bù xī', 'constantly strive to become stronger', '自己努力向上，永不懈怠', '出自《周易》，象征着永不放弃的精神品质。', 4, 90),
                ('厚德载物', 'hòu dé zài wù', 'virtue carries all things', '以深厚的德泽育人利物', '出自《周易》，强调品德修养的重要性。', 4, 88),
                ('知行合一', 'zhī xíng hé yī', 'unity of knowledge and action', '认识与实践的统一', '王阳明心学的核心理念，强调理论与实践的结合。', 4, 85)
            """
                )
            )

            conn.commit()
            print("✓ 示例数据插入成功")

    except Exception as e:
        print(f"❌ 插入示例数据失败: {e}")
        return False

    return True


def main():
    """主函数"""
    print("=== 汉字纹身AI平台MySQL数据库初始化 ===")

    # 创建数据库
    if not create_database():
        sys.exit(1)

    # 创建数据表
    if not create_tables():
        sys.exit(1)

    # 插入示例数据
    if not insert_sample_data():
        sys.exit(1)

    print("\n🎉 MySQL数据库初始化完成！")
    print("现在可以启动后端服务了：")
    print("uvicorn main:app --reload --host 0.0.0.0 --port 8000")


if __name__ == "__main__":
    main()
