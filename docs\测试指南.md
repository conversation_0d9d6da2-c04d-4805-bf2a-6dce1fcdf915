# 汉字纹身AI平台测试指南

## 🚀 快速启动

### 1. 环境准备

**必需软件：**
- Python 3.11+
- Node.js 18+
- PostgreSQL 15+

### 2. 数据库设置

```bash
# 启动PostgreSQL服务
# Windows: 在服务中启动PostgreSQL
# macOS: brew services start postgresql
# Linux: sudo systemctl start postgresql

# 连接到PostgreSQL并创建数据库
psql -U postgres
CREATE DATABASE chinese_tattoo_ai;
\q
```

### 3. 一键启动

在项目根目录运行：

```powershell
# Windows PowerShell
.\scripts\start-local.ps1
```

或者手动启动：

### 4. 手动启动步骤

#### 后端启动：

```bash
# 1. 进入后端目录
cd backend

# 2. 创建虚拟环境
python -m venv venv

# 3. 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt

# 5. 复制环境配置
cp .env.example .env

# 6. 初始化数据库
python init_db.py

# 7. 启动后端服务
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端启动：

```bash
# 1. 进入前端目录
cd frontend

# 2. 安装依赖
npm install

# 3. 创建环境配置
echo "VITE_API_BASE_URL=http://localhost:8000/api/v1" > .env.local

# 4. 启动前端服务
npm run dev
```

## 🧪 功能测试

### 1. 基础功能测试

**访问地址：**
- 前端应用: http://localhost:5173
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 2. 用户注册登录测试

1. 访问 http://localhost:5173
2. 点击"注册"按钮
3. 填写注册信息：
   - 邮箱: <EMAIL>
   - 用户名: testuser
   - 密码: 123456
4. 注册成功后登录

### 3. 搜索功能测试

1. 登录后点击"搜索"
2. 输入关键词测试：
   - 中文：爱、智、勇
   - 英文：love、wisdom、strength
   - 拼音：ai、zhi、yong
3. 验证搜索结果显示

### 4. AI推荐功能测试

1. 点击"AI推荐"
2. 输入关键词：love, strength, wisdom
3. 填写描述：I want a tattoo that represents inner strength
4. 点击"获取AI推荐"
5. 查看推荐结果

### 5. 纹身设计功能测试

1. 点击"纹身设计"
2. 输入汉字：智
3. 选择风格：书法体
4. 选择部位：前臂
5. 点击"生成纹身设计"
6. 查看生成结果

## 🔧 常见问题解决

### 1. 数据库连接失败

**错误信息：** `could not connect to server`

**解决方案：**
```bash
# 检查PostgreSQL服务状态
# Windows:
services.msc # 查找PostgreSQL服务

# 确保数据库存在
psql -U postgres -l | grep chinese_tattoo_ai
```

### 2. Python依赖安装失败

**错误信息：** `pip install failed`

**解决方案：**
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 3. 前端启动失败

**错误信息：** `npm install failed`

**解决方案：**
```bash
# 清除缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules
npm install

# 使用yarn替代
yarn install
```

### 4. AI功能不可用

**原因：** 没有配置AI API密钥

**解决方案：**
1. 编辑 `backend/.env` 文件
2. 添加API密钥（可选）：
```
OPENAI_API_KEY=your_openai_key_here
STABILITY_AI_API_KEY=your_stability_key_here
```
3. 如果没有API密钥，系统会使用模拟数据

## 📊 测试数据

系统会自动创建以下测试数据：

### 示例汉字：
- 爱 (ài) - love, affection
- 智 (zhì) - wisdom, intelligence  
- 勇 (yǒng) - courage, bravery
- 和 (hé) - harmony, peace
- 福 (fú) - fortune, blessing

### 示例成语：
- 自强不息 - constantly strive to become stronger
- 厚德载物 - virtue carries all things
- 知行合一 - unity of knowledge and action

### 纹身风格：
- 书法体 (calligraphy)
- 现代简约 (modern)
- 水彩风格 (watercolor)
- 部落风格 (tribal)

## 🎯 测试检查清单

- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 搜索功能返回结果
- [ ] AI推荐功能可用
- [ ] 纹身设计生成功能
- [ ] 页面响应式设计
- [ ] 导航菜单功能
- [ ] 用户仪表板显示
- [ ] 积分系统功能
- [ ] 图片下载功能

## 🚨 紧急问题联系

如果遇到无法解决的问题，请检查：

1. **日志文件：**
   - 后端日志：终端输出
   - 前端日志：浏览器控制台

2. **端口占用：**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :8000
   netstat -ano | findstr :5173
   ```

3. **防火墙设置：**
   确保8000和5173端口未被防火墙阻止

## 🎉 测试成功标志

当您看到以下内容时，说明系统运行正常：

1. 前端页面正常显示
2. 用户可以注册和登录
3. 搜索功能返回结果
4. AI推荐显示内容（即使是模拟数据）
5. 纹身设计可以生成预览图

祝您测试顺利！🚀
