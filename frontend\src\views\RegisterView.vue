<template>
  <div class="register-container">
    <el-card class="register-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h2>注册账户 / Register</h2>
          <p>加入墨痕智纹，开启您的汉字纹身之旅</p>
        </div>
      </template>

      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        label-width="80px"
        size="large"
      >
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="registerForm.email"
            type="email"
            placeholder="请输入邮箱地址"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item label="全名" prop="full_name">
          <el-input
            v-model="registerForm.full_name"
            placeholder="请输入您的全名（可选）"
            prefix-icon="Avatar"
            clearable
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            prefix-icon="Lock"
            show-password
            clearable
            @keyup.enter="handleRegister"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="agreeTerms">
            我已阅读并同意
            <el-link type="primary" @click="showTerms">《服务条款》</el-link>
            和
            <el-link type="primary" @click="showPrivacy">《隐私政策》</el-link>
          </el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="userStore.isLoading"
            :disabled="!agreeTerms"
            @click="handleRegister"
          >
            注册账户
          </el-button>
        </el-form-item>

        <el-form-item>
          <div class="register-footer">
            <span>已有账户？</span>
            <el-link type="primary" @click="$router.push('/login')">
              立即登录
            </el-link>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 服务条款对话框 -->
    <el-dialog
      v-model="showTermsDialog"
      title="服务条款"
      width="80%"
      max-width="600px"
    >
      <div class="terms-content">
        <h3>墨痕智纹服务条款</h3>
        <p>欢迎使用墨痕智纹汉字纹身AI平台。请仔细阅读以下条款：</p>
        
        <h4>1. 服务说明</h4>
        <p>本平台提供汉字文化解释和纹身设计建议服务，旨在帮助用户了解汉字的文化内涵。</p>
        
        <h4>2. 用户责任</h4>
        <p>用户应理性对待纹身决定，充分了解汉字含义后再做选择。平台不对用户的纹身决定承担责任。</p>
        
        <h4>3. 文化尊重</h4>
        <p>请尊重中华文化，避免将汉字用于不当用途。</p>
        
        <h4>4. 知识产权</h4>
        <p>平台生成的内容受知识产权保护，用户获得个人使用权。</p>
      </div>
      
      <template #footer>
        <el-button @click="showTermsDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 隐私政策对话框 -->
    <el-dialog
      v-model="showPrivacyDialog"
      title="隐私政策"
      width="80%"
      max-width="600px"
    >
      <div class="privacy-content">
        <h3>隐私政策</h3>
        <p>我们重视您的隐私保护，本政策说明我们如何收集、使用和保护您的信息：</p>
        
        <h4>1. 信息收集</h4>
        <p>我们收集您提供的注册信息、使用偏好和服务使用记录。</p>
        
        <h4>2. 信息使用</h4>
        <p>收集的信息用于提供个性化服务、改进产品和客户支持。</p>
        
        <h4>3. 信息保护</h4>
        <p>我们采用行业标准的安全措施保护您的个人信息。</p>
        
        <h4>4. 信息共享</h4>
        <p>除法律要求外，我们不会与第三方共享您的个人信息。</p>
      </div>
      
      <template #footer>
        <el-button @click="showPrivacyDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useUserStore } from '@/stores/counter'
import type { RegisterRequest } from '@/types'

const router = useRouter()
const userStore = useUserStore()

const registerFormRef = ref<FormInstance>()
const agreeTerms = ref(false)
const showTermsDialog = ref(false)
const showPrivacyDialog = ref(false)

// 注册表单数据
const registerForm = reactive<RegisterRequest & { confirmPassword: string }>({
  email: '',
  username: '',
  password: '',
  confirmPassword: '',
  full_name: ''
})

// 自定义验证规则
const validatePassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 6) {
    callback(new Error('密码长度至少6位'))
  } else {
    if (registerForm.confirmPassword !== '') {
      registerFormRef.value?.validateField('confirmPassword')
    }
    callback()
  }
}

const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

// 表单验证规则
const registerRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  password: [
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  if (!agreeTerms.value) {
    ElMessage.warning('请先同意服务条款和隐私政策')
    return
  }

  try {
    await registerFormRef.value.validate()
    
    const { confirmPassword, ...registerData } = registerForm
    await userStore.register(registerData)
    
    ElMessage.success('注册成功！请登录您的账户')
    router.push('/login')
    
  } catch (error) {
    console.error('注册失败:', error)
  }
}

// 显示服务条款
const showTerms = () => {
  showTermsDialog.value = true
}

// 显示隐私政策
const showPrivacy = () => {
  showPrivacyDialog.value = true
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.register-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.register-card {
  width: 100%;
  max-width: 450px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  position: relative;
  z-index: 1;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-weight: 600;
  font-family: 'SimSun', serif;
}

.card-header p {
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-style: italic;
}

.register-footer {
  width: 100%;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.register-footer span {
  margin-right: 8px;
}

.terms-content,
.privacy-content {
  line-height: 1.6;
  color: #333;
}

.terms-content h3,
.privacy-content h3 {
  color: #1a1a1a;
  margin-bottom: 16px;
}

.terms-content h4,
.privacy-content h4 {
  color: #333;
  margin: 16px 0 8px 0;
  font-size: 14px;
}

.terms-content p,
.privacy-content p {
  margin-bottom: 12px;
  font-size: 14px;
}

:deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

:deep(.el-card__header) {
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-card__body) {
  background: transparent;
}

:deep(.el-checkbox__label) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-dialog) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}
</style>
