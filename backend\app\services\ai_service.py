"""
AI服务
AI Service for character recommendations and image generation
"""

import openai
import json
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from app.core.config import settings
from app.models.character import ChineseCharacter, ChineseIdiom
from app.schemas.character import (
    RecommendationResponse, CharacterRecommendation, IdiomRecommendation
)

class AIService:
    """AI服务类"""
    
    def __init__(self):
        if settings.OPENAI_API_KEY:
            openai.api_key = settings.OPENAI_API_KEY
    
    async def get_recommendations(
        self,
        keywords: List[str],
        description: Optional[str] = None,
        recommendation_type: str = "both",
        max_results: int = 5,
        db: Session = None
    ) -> RecommendationResponse:
        """
        获取AI推荐的汉字和成语
        """
        
        # 构建提示词
        prompt = self._build_recommendation_prompt(
            keywords, description, recommendation_type, max_results
        )
        
        try:
            # 调用OpenAI API
            response = await self._call_openai_api(prompt)
            
            # 解析AI响应
            ai_data = self._parse_ai_response(response)
            
            # 验证和丰富推荐结果
            recommendations = await self._enrich_recommendations(ai_data, db)
            
            return recommendations
            
        except Exception as e:
            # 如果AI服务失败，返回基于数据库的推荐
            return await self._fallback_recommendations(
                keywords, recommendation_type, max_results, db
            )
    
    def _build_recommendation_prompt(
        self,
        keywords: List[str],
        description: Optional[str],
        recommendation_type: str,
        max_results: int
    ) -> str:
        """构建AI推荐提示词"""
        
        keywords_str = ", ".join(keywords)
        
        prompt = f"""
你是一位精通中华文化和汉字的专家，专门为外国人推荐适合纹身的汉字和成语。

用户关键词：{keywords_str}
"""
        
        if description:
            prompt += f"用户描述：{description}\n"
        
        prompt += f"""
请根据用户的需求，推荐最合适的汉字和成语用于纹身。要求：

1. 推荐内容要文化准确，避免误解
2. 适合纹身，视觉效果好
3. 含义深刻，符合用户需求
4. 提供详细的文化背景解释

请以JSON格式返回，包含以下字段：
{{
    "characters": [
        {{
            "character": "汉字",
            "pinyin": "拼音",
            "meaning_en": "英文含义",
            "meaning_zh": "中文含义", 
            "cultural_context": "文化背景",
            "score": 0.95,
            "explanation": "推荐理由"
        }}
    ],
    "idioms": [
        {{
            "idiom": "成语",
            "pinyin": "拼音",
            "meaning_en": "英文含义",
            "meaning_zh": "中文含义",
            "origin_story": "典故来源",
            "score": 0.92,
            "explanation": "推荐理由"
        }}
    ],
    "ai_explanation": "整体推荐说明"
}}

推荐类型：{recommendation_type}
最大结果数：{max_results}
"""
        
        return prompt
    
    async def _call_openai_api(self, prompt: str) -> str:
        """调用OpenAI API"""
        
        if not settings.OPENAI_API_KEY:
            raise Exception("OpenAI API密钥未配置")
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": "你是一位专业的中华文化专家和汉字学者，专门为外国人提供汉字纹身建议。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=2000,
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            raise Exception(f"OpenAI API调用失败: {str(e)}")
    
    def _parse_ai_response(self, response: str) -> Dict[str, Any]:
        """解析AI响应"""
        try:
            # 尝试提取JSON部分
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                return json.loads(json_str)
            else:
                raise Exception("无法找到有效的JSON响应")
                
        except json.JSONDecodeError as e:
            raise Exception(f"JSON解析失败: {str(e)}")
    
    async def _enrich_recommendations(
        self, ai_data: Dict[str, Any], db: Session
    ) -> RecommendationResponse:
        """验证和丰富推荐结果"""
        
        characters = []
        idioms = []
        
        # 处理汉字推荐
        if "characters" in ai_data:
            for char_data in ai_data["characters"]:
                try:
                    character_rec = CharacterRecommendation(
                        character=char_data.get("character", ""),
                        pinyin=char_data.get("pinyin", ""),
                        meaning_en=char_data.get("meaning_en", ""),
                        meaning_zh=char_data.get("meaning_zh", ""),
                        cultural_context=char_data.get("cultural_context"),
                        score=float(char_data.get("score", 0.5)),
                        explanation=char_data.get("explanation", "")
                    )
                    characters.append(character_rec)
                except Exception:
                    continue
        
        # 处理成语推荐
        if "idioms" in ai_data:
            for idiom_data in ai_data["idioms"]:
                try:
                    idiom_rec = IdiomRecommendation(
                        idiom=idiom_data.get("idiom", ""),
                        pinyin=idiom_data.get("pinyin", ""),
                        meaning_en=idiom_data.get("meaning_en", ""),
                        meaning_zh=idiom_data.get("meaning_zh", ""),
                        origin_story=idiom_data.get("origin_story"),
                        score=float(idiom_data.get("score", 0.5)),
                        explanation=idiom_data.get("explanation", "")
                    )
                    idioms.append(idiom_rec)
                except Exception:
                    continue
        
        ai_explanation = ai_data.get("ai_explanation", "基于您的需求，我为您推荐了以下汉字和成语。")
        
        return RecommendationResponse(
            characters=characters,
            idioms=idioms,
            ai_explanation=ai_explanation
        )
    
    async def _fallback_recommendations(
        self,
        keywords: List[str],
        recommendation_type: str,
        max_results: int,
        db: Session
    ) -> RecommendationResponse:
        """备用推荐方案（基于数据库）"""
        
        characters = []
        idioms = []
        
        # 简单的关键词匹配
        keyword_str = " ".join(keywords).lower()
        
        if recommendation_type in ["character", "both"] and db:
            db_characters = db.query(ChineseCharacter).filter(
                ChineseCharacter.is_active == True,
                ChineseCharacter.meaning_en.ilike(f"%{keyword_str}%")
            ).order_by(ChineseCharacter.tattoo_popularity.desc()).limit(max_results).all()
            
            for char in db_characters:
                character_rec = CharacterRecommendation(
                    character=char.character,
                    pinyin=char.pinyin,
                    meaning_en=char.meaning_en,
                    meaning_zh=char.meaning_zh,
                    cultural_context=char.cultural_context,
                    score=0.8,
                    explanation=f"基于关键词匹配推荐的汉字：{char.character}"
                )
                characters.append(character_rec)
        
        if recommendation_type in ["idiom", "both"] and db:
            db_idioms = db.query(ChineseIdiom).filter(
                ChineseIdiom.is_active == True,
                ChineseIdiom.meaning_en.ilike(f"%{keyword_str}%")
            ).order_by(ChineseIdiom.tattoo_popularity.desc()).limit(max_results).all()
            
            for idiom in db_idioms:
                idiom_rec = IdiomRecommendation(
                    idiom=idiom.idiom,
                    pinyin=idiom.pinyin,
                    meaning_en=idiom.meaning_en,
                    meaning_zh=idiom.meaning_zh,
                    origin_story=idiom.origin_story,
                    score=0.8,
                    explanation=f"基于关键词匹配推荐的成语：{idiom.idiom}"
                )
                idioms.append(idiom_rec)
        
        return RecommendationResponse(
            characters=characters,
            idioms=idioms,
            ai_explanation="AI服务暂时不可用，为您提供基于数据库的推荐结果。"
        )
