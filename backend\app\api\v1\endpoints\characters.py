"""
汉字成语相关API端点
Character and Idiom Related API Endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from app.db.database import get_db
from app.models.character import ChineseCharacter, ChineseIdiom
from app.models.user import User
from app.schemas.character import (
    CharacterResponse, IdiomResponse, SearchResponse,
    RecommendationRequest, RecommendationResponse
)
from app.services.auth_service import AuthService
from app.services.ai_service import AIService

router = APIRouter()

@router.get("/search", response_model=SearchResponse, summary="搜索汉字和成语")
async def search_characters_and_idioms(
    q: str = Query(..., description="搜索关键词"),
    type: str = Query("both", description="搜索类型: character, idiom, both"),
    limit: int = Query(10, ge=1, le=50, description="返回结果数量限制"),
    db: Session = Depends(get_db)
):
    """
    搜索汉字和成语
    
    - **q**: 搜索关键词（英文或中文）
    - **type**: 搜索类型 - character（仅汉字）, idiom（仅成语）, both（两者）
    - **limit**: 返回结果数量限制
    """
    characters = []
    idioms = []
    
    if type in ["character", "both"]:
        # 搜索汉字
        character_query = db.query(ChineseCharacter).filter(
            ChineseCharacter.is_active == True
        )
        
        # 支持中文字符、拼音、英文含义搜索
        character_query = character_query.filter(
            (ChineseCharacter.character.ilike(f"%{q}%")) |
            (ChineseCharacter.pinyin.ilike(f"%{q}%")) |
            (ChineseCharacter.meaning_en.ilike(f"%{q}%")) |
            (ChineseCharacter.meaning_zh.ilike(f"%{q}%"))
        )
        
        characters = character_query.order_by(
            ChineseCharacter.tattoo_popularity.desc()
        ).limit(limit).all()
    
    if type in ["idiom", "both"]:
        # 搜索成语
        idiom_query = db.query(ChineseIdiom).filter(
            ChineseIdiom.is_active == True
        )
        
        # 支持成语、拼音、英文含义搜索
        idiom_query = idiom_query.filter(
            (ChineseIdiom.idiom.ilike(f"%{q}%")) |
            (ChineseIdiom.pinyin.ilike(f"%{q}%")) |
            (ChineseIdiom.meaning_en.ilike(f"%{q}%")) |
            (ChineseIdiom.meaning_zh.ilike(f"%{q}%"))
        )
        
        idioms = idiom_query.order_by(
            ChineseIdiom.tattoo_popularity.desc()
        ).limit(limit).all()
    
    total = len(characters) + len(idioms)
    
    return SearchResponse(
        characters=characters,
        idioms=idioms,
        total=total
    )

@router.post("/recommend", response_model=RecommendationResponse, summary="获取AI推荐")
async def get_ai_recommendations(
    request: RecommendationRequest,
    current_user: User = Depends(AuthService.get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    基于用户输入获取AI推荐的汉字和成语
    
    - **keywords**: 关键词列表
    - **description**: 详细描述（可选）
    - **type**: 推荐类型 - character, idiom, both
    - **max_results**: 最大返回结果数
    """
    
    # 检查用户积分（免费用户有限制）
    if not current_user.is_premium and current_user.credits < 1:
        raise HTTPException(
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            detail="积分不足，请充值或升级为高级用户"
        )
    
    try:
        # 调用AI服务获取推荐
        ai_service = AIService()
        recommendations = await ai_service.get_recommendations(
            keywords=request.keywords,
            description=request.description,
            recommendation_type=request.type,
            max_results=request.max_results,
            db=db
        )
        
        # 扣除积分（免费用户）
        if not current_user.is_premium:
            current_user.credits -= 1
            db.commit()
        
        return recommendations
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI推荐服务暂时不可用: {str(e)}"
        )

@router.get("/characters/{character_id}", response_model=CharacterResponse, summary="获取汉字详情")
async def get_character_detail(
    character_id: int,
    db: Session = Depends(get_db)
):
    """获取指定汉字的详细信息"""
    character = db.query(ChineseCharacter).filter(
        ChineseCharacter.id == character_id,
        ChineseCharacter.is_active == True
    ).first()
    
    if not character:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="汉字不存在"
        )
    
    return character

@router.get("/idioms/{idiom_id}", response_model=IdiomResponse, summary="获取成语详情")
async def get_idiom_detail(
    idiom_id: int,
    db: Session = Depends(get_db)
):
    """获取指定成语的详细信息"""
    idiom = db.query(ChineseIdiom).filter(
        ChineseIdiom.id == idiom_id,
        ChineseIdiom.is_active == True
    ).first()
    
    if not idiom:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="成语不存在"
        )
    
    return idiom

@router.get("/popular", summary="获取热门汉字和成语")
async def get_popular_content(
    type: str = Query("both", description="内容类型"),
    limit: int = Query(10, ge=1, le=20),
    db: Session = Depends(get_db)
):
    """获取最受欢迎的汉字和成语"""
    result = {}
    
    if type in ["character", "both"]:
        popular_characters = db.query(ChineseCharacter).filter(
            ChineseCharacter.is_active == True
        ).order_by(
            ChineseCharacter.tattoo_popularity.desc()
        ).limit(limit).all()
        result["characters"] = popular_characters
    
    if type in ["idiom", "both"]:
        popular_idioms = db.query(ChineseIdiom).filter(
            ChineseIdiom.is_active == True
        ).order_by(
            ChineseIdiom.tattoo_popularity.desc()
        ).limit(limit).all()
        result["idioms"] = popular_idioms
    
    return result
