<template>
  <div class="tattoo-design-page">
    <!-- 页面头部 -->
    <section class="design-header">
      <div class="container">
        <div class="header-content">
          <h1>纹身设计生成</h1>
          <p>为您的汉字创造独一无二的纹身设计</p>
        </div>
      </div>
    </section>

    <!-- 设计工作区 -->
    <section class="design-workspace">
      <div class="container">
        <div class="workspace-grid">
          <!-- 左侧：设计配置 -->
          <div class="design-config">
            <el-card class="config-card">
              <template #header>
                <h3>设计配置</h3>
              </template>

              <el-form
                ref="designFormRef"
                :model="designForm"
                :rules="designRules"
                label-width="100px"
                size="large"
              >
                <!-- 汉字/成语内容 -->
                <el-form-item label="内容" prop="content">
                  <el-input
                    v-model="designForm.content"
                    placeholder="输入汉字或成语"
                    size="large"
                    class="content-input"
                  >
                    <template #prepend>
                      <el-select v-model="designForm.type" style="width: 100px">
                        <el-option label="汉字" value="character" />
                        <el-option label="成语" value="idiom" />
                      </el-select>
                    </template>
                  </el-input>
                </el-form-item>

                <!-- 纹身风格 -->
                <el-form-item label="风格" prop="style">
                  <el-select
                    v-model="designForm.style"
                    placeholder="选择纹身风格"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="style in tattooStyles"
                      :key="style.name"
                      :label="style.name_zh"
                      :value="style.name"
                    >
                      <div class="style-option">
                        <span class="style-name">{{ style.name_zh }}</span>
                        <span class="style-desc">{{ style.description }}</span>
                      </div>
                    </el-option>
                  </el-select>
                </el-form-item>

                <!-- 身体部位 -->
                <el-form-item label="部位" prop="position">
                  <el-select
                    v-model="designForm.position"
                    placeholder="选择身体部位"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="position in bodyPositions"
                      :key="position.value"
                      :label="position.label"
                      :value="position.value"
                    />
                  </el-select>
                </el-form-item>

                <!-- 图片质量 -->
                <el-form-item label="图片质量">
                  <el-radio-group v-model="designForm.high_resolution">
                    <el-radio :label="false">
                      <div class="quality-option">
                        <div class="quality-name">标准质量</div>
                        <div class="quality-desc">512x512 像素，消耗 2 积分</div>
                      </div>
                    </el-radio>
                    <el-radio :label="true">
                      <div class="quality-option">
                        <div class="quality-name">高清质量</div>
                        <div class="quality-desc">1024x1024 像素，消耗 5 积分</div>
                      </div>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>

                <!-- 积分信息 -->
                <el-form-item>
                  <div class="credits-info">
                    <div class="current-credits">
                      <el-icon><Coin /></el-icon>
                      当前积分：{{ userStore.user?.credits || 0 }}
                    </div>
                    <div class="required-credits">
                      需要积分：{{ requiredCredits }}
                    </div>
                  </div>
                </el-form-item>

                <!-- 生成按钮 -->
                <el-form-item>
                  <el-button
                    type="primary"
                    size="large"
                    @click="generateTattooDesign"
                    :loading="isGenerating"
                    :disabled="!canGenerate"
                    style="width: 100%"
                  >
                    <el-icon><Picture /></el-icon>
                    生成纹身设计
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>

            <!-- 风格预览 -->
            <el-card class="style-preview-card" v-if="selectedStyle">
              <template #header>
                <h4>风格预览</h4>
              </template>
              <div class="style-preview">
                <h5>{{ selectedStyle.name_zh }}</h5>
                <p>{{ selectedStyle.description }}</p>
                <div class="style-characteristics" v-if="selectedStyle.characteristics">
                  <div class="characteristic" v-for="(value, key) in selectedStyle.characteristics" :key="key">
                    <strong>{{ key }}:</strong> {{ value }}
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 右侧：预览区域 -->
          <div class="design-preview">
            <!-- 生成中状态 -->
            <div v-if="isGenerating" class="generating-state">
              <div class="generating-animation">
                <div class="chinese-char-animation">{{ designForm.content || '智' }}</div>
                <div class="generating-text">
                  <h3>AI正在为您生成纹身设计...</h3>
                  <p>这可能需要几秒钟时间</p>
                  <el-progress :percentage="generationProgress" :show-text="false" />
                </div>
              </div>
            </div>

            <!-- 预览结果 -->
            <div v-else-if="generatedImage" class="preview-result">
              <div class="image-container">
                <img 
                  :src="generatedImage.image_url" 
                  :alt="`${designForm.content} 纹身设计`"
                  class="generated-image"
                  @load="handleImageLoad"
                />
                <div class="image-overlay" v-if="generatedImage.is_watermarked">
                  <div class="watermark-notice">
                    <el-icon><View /></el-icon>
                    预览版本（含水印）
                  </div>
                </div>
              </div>

              <div class="image-info">
                <div class="image-meta">
                  <span>{{ generatedImage.width }}x{{ generatedImage.height }}</span>
                  <span>生成时间: {{ generatedImage.generation_time?.toFixed(1) }}s</span>
                  <span v-if="generatedImage.quality_score">
                    质量评分: {{ (generatedImage.quality_score * 100).toFixed(0) }}%
                  </span>
                </div>

                <div class="image-actions">
                  <el-button @click="downloadImage" :disabled="!generatedImage.image_url">
                    <el-icon><Download /></el-icon>
                    下载预览图
                  </el-button>
                  
                  <el-button 
                    type="primary" 
                    @click="unlockHighResolution"
                    v-if="generatedImage.is_watermarked"
                    :loading="isUnlocking"
                  >
                    <el-icon><Unlock /></el-icon>
                    解锁高清版 (10积分)
                  </el-button>

                  <el-button @click="regenerateDesign">
                    <el-icon><Refresh /></el-icon>
                    重新生成
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 默认状态 -->
            <div v-else class="default-preview">
              <div class="preview-placeholder">
                <el-icon size="64"><Picture /></el-icon>
                <h3>纹身设计预览</h3>
                <p>配置您的设计参数，然后点击生成按钮</p>
                <div class="preview-demo">
                  <div class="demo-character">{{ designForm.content || '智' }}</div>
                  <div class="demo-style">{{ selectedStyle?.name_zh || '书法体' }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 历史设计 -->
    <section class="design-history" v-if="designHistory.length > 0">
      <div class="container">
        <h2>您的设计历史</h2>
        <div class="history-grid">
          <div 
            v-for="design in designHistory" 
            :key="design.id"
            class="history-item"
            @click="loadHistoryDesign(design)"
          >
            <img :src="design.thumbnail_url || design.image_url" :alt="design.content" />
            <div class="history-info">
              <div class="content">{{ design.content }}</div>
              <div class="style">{{ design.style }}</div>
              <div class="date">{{ formatDate(design.created_at) }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 积分不足对话框 -->
    <el-dialog
      v-model="showCreditDialog"
      title="积分不足"
      width="400px"
      center
    >
      <div class="credit-dialog-content">
        <el-icon size="48" color="#ffd700"><Coin /></el-icon>
        <p>您的积分不足，无法生成纹身设计</p>
        <p>当前积分：{{ userStore.user?.credits || 0 }}</p>
        <p>所需积分：{{ requiredCredits }}</p>
      </div>
      <template #footer>
        <el-button @click="showCreditDialog = false">取消</el-button>
        <el-button type="primary" @click="goToRecharge">充值积分</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { 
  Picture, Coin, Download, Unlock, Refresh, View 
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/counter'
import { TattooService } from '@/services/tattoo'
import type { 
  TattooStyle, 
  TattooImage, 
  TattooRequest 
} from '@/types'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const designFormRef = ref<FormInstance>()
const isGenerating = ref(false)
const isUnlocking = ref(false)
const generationProgress = ref(0)
const showCreditDialog = ref(false)

// 设计表单
const designForm = reactive({
  content: '',
  type: 'character',
  style: 'calligraphy',
  position: 'forearm',
  high_resolution: false
})

// 生成的图片
const generatedImage = ref<TattooImage | null>(null)

// 纹身风格列表
const tattooStyles = ref<TattooStyle[]>([])

// 身体部位选项
const bodyPositions = ref([
  { label: '前臂', value: 'forearm' },
  { label: '上臂', value: 'upper_arm' },
  { label: '肩膀', value: 'shoulder' },
  { label: '背部', value: 'back' },
  { label: '胸部', value: 'chest' },
  { label: '手腕', value: 'wrist' },
  { label: '脚踝', value: 'ankle' },
  { label: '颈部', value: 'neck' }
])

// 设计历史
const designHistory = ref<TattooImage[]>([])

// 计算属性
const selectedStyle = computed(() => {
  return tattooStyles.value.find(style => style.name === designForm.style)
})

const requiredCredits = computed(() => {
  return designForm.high_resolution ? 5 : 2
})

const canGenerate = computed(() => {
  const hasContent = designForm.content.trim().length > 0
  const hasCredits = userStore.user?.is_premium || 
    (userStore.user?.credits || 0) >= requiredCredits.value
  return hasContent && hasCredits && !isGenerating.value
})

// 表单验证规则
const designRules: FormRules = {
  content: [
    { required: true, message: '请输入汉字或成语', trigger: 'blur' }
  ],
  style: [
    { required: true, message: '请选择纹身风格', trigger: 'change' }
  ],
  position: [
    { required: true, message: '请选择身体部位', trigger: 'change' }
  ]
}

onMounted(async () => {
  // 检查用户登录状态
  if (!userStore.isAuthenticated) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  // 从URL参数获取内容
  const content = route.query.content as string
  const type = route.query.type as string
  
  if (content) {
    designForm.content = content
  }
  if (type) {
    designForm.type = type
  }

  // 加载纹身风格
  await loadTattooStyles()
  
  // 加载设计历史
  await loadDesignHistory()
})

// 监听积分变化
watch(() => designForm.high_resolution, () => {
  // 检查积分是否足够
  if (!canGenerate.value && designForm.content) {
    ElMessage.warning(`生成${designForm.high_resolution ? '高清' : '标准'}质量图片需要 ${requiredCredits.value} 积分`)
  }
})

// 加载纹身风格
const loadTattooStyles = async () => {
  try {
    const styles = await TattooService.getTattooStyles()
    tattooStyles.value = styles
  } catch (error) {
    console.error('加载纹身风格失败:', error)
  }
}

// 加载设计历史
const loadDesignHistory = async () => {
  try {
    const history = await TattooService.getUserTattooHistory()
    designHistory.value = history.slice(0, 6) // 只显示最近6个
  } catch (error) {
    console.error('加载设计历史失败:', error)
  }
}

// 生成纹身设计
const generateTattooDesign = async () => {
  if (!designFormRef.value) return

  try {
    await designFormRef.value.validate()

    // 检查积分
    if (!userStore.user?.is_premium && (userStore.user?.credits || 0) < requiredCredits.value) {
      showCreditDialog.value = true
      return
    }

    isGenerating.value = true
    generationProgress.value = 0

    // 模拟进度
    const progressInterval = setInterval(() => {
      if (generationProgress.value < 90) {
        generationProgress.value += Math.random() * 20
      }
    }, 500)

    try {
      const result = await TattooService.generateTattooImage({
        content: designForm.content,
        style: designForm.style,
        position: designForm.position,
        high_resolution: designForm.high_resolution
      })

      generatedImage.value = result
      generationProgress.value = 100

      // 更新用户积分
      if (!userStore.user?.is_premium) {
        userStore.updateCredits((userStore.user?.credits || 0) - requiredCredits.value)
      }

      ElMessage.success('纹身设计生成成功！')

      // 刷新历史记录
      await loadDesignHistory()

    } finally {
      clearInterval(progressInterval)
    }

  } catch (error) {
    console.error('生成纹身设计失败:', error)
    ElMessage.error('生成失败，请稍后重试')
  } finally {
    isGenerating.value = false
    generationProgress.value = 0
  }
}

// 处理图片加载
const handleImageLoad = () => {
  // 图片加载完成后的处理
}

// 下载图片
const downloadImage = () => {
  if (!generatedImage.value?.image_url) return
  
  const link = document.createElement('a')
  link.href = generatedImage.value.image_url
  link.download = `tattoo-${designForm.content}-${Date.now()}.png`
  link.click()
}

// 解锁高清版本
const unlockHighResolution = async () => {
  if (!generatedImage.value) return

  // 检查积分
  if (!userStore.user?.is_premium && (userStore.user?.credits || 0) < 10) {
    showCreditDialog.value = true
    return
  }

  isUnlocking.value = true

  try {
    const result = await TattooService.unlockHighResolution(generatedImage.value.id)
    generatedImage.value = result

    // 更新用户积分
    if (!userStore.user?.is_premium) {
      userStore.updateCredits((userStore.user?.credits || 0) - 10)
    }

    ElMessage.success('高清版本解锁成功！')

  } catch (error) {
    console.error('解锁高清版本失败:', error)
    ElMessage.error('解锁失败，请稍后重试')
  } finally {
    isUnlocking.value = false
  }
}

// 重新生成设计
const regenerateDesign = () => {
  generatedImage.value = null
  generateTattooDesign()
}

// 加载历史设计
const loadHistoryDesign = (design: TattooImage) => {
  designForm.content = design.content
  designForm.style = design.style
  designForm.position = design.position
  generatedImage.value = design
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 前往充值
const goToRecharge = () => {
  showCreditDialog.value = false
  router.push('/profile?tab=credits')
}
</script>

<style scoped>
.tattoo-design-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 页面头部 */
.design-header {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-content {
  text-align: center;
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
  font-family: 'SimSun', serif;
}

.header-content p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
}

/* 设计工作区 */
.design-workspace {
  padding: 40px 0;
}

.workspace-grid {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 40px;
  align-items: start;
}

/* 左侧配置区域 */
.design-config {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.config-card h3 {
  color: #ffffff;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.content-input {
  font-size: 1.2rem;
}

:deep(.content-input .el-input__inner) {
  font-size: 1.2rem;
  font-family: 'SimSun', serif;
  text-align: center;
}

.style-option {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.style-name {
  font-weight: 600;
  color: #ffffff;
}

.style-desc {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
}

.quality-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.quality-name {
  font-weight: 500;
  color: #ffffff;
}

.quality-desc {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.6);
}

.credits-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 215, 0, 0.1);
  border: 1px solid rgba(255, 215, 0, 0.2);
  border-radius: 8px;
}

.current-credits,
.required-credits {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #ffd700;
  font-weight: 600;
}

/* 风格预览卡片 */
.style-preview-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.style-preview h5 {
  color: #ffd700;
  margin: 0 0 8px 0;
  font-size: 1.1rem;
}

.style-preview p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 12px 0;
  font-size: 0.9rem;
}

.style-characteristics {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.characteristic {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
}

/* 右侧预览区域 */
.design-preview {
  min-height: 600px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 生成中状态 */
.generating-state {
  text-align: center;
  padding: 40px;
}

.generating-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
}

.chinese-char-animation {
  font-size: 6rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  animation: pulse 2s ease-in-out infinite;
  text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
}

.generating-text h3 {
  color: #ffffff;
  margin: 0 0 8px 0;
  font-size: 1.5rem;
}

.generating-text p {
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 20px 0;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

/* 预览结果 */
.preview-result {
  width: 100%;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.image-container {
  position: relative;
  max-width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.generated-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 12px;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding: 16px;
}

.watermark-notice {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  backdrop-filter: blur(10px);
}

.image-info {
  width: 100%;
  text-align: center;
}

.image-meta {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
}

.image-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 默认预览状态 */
.default-preview {
  text-align: center;
  padding: 60px 40px;
}

.preview-placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.preview-placeholder h3 {
  color: #ffffff;
  margin: 16px 0 8px 0;
  font-size: 1.5rem;
}

.preview-placeholder p {
  margin: 0 0 30px 0;
  font-size: 1rem;
}

.preview-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 2px dashed rgba(255, 255, 255, 0.2);
}

.demo-character {
  font-size: 4rem;
  font-weight: bold;
  color: rgba(255, 215, 0, 0.6);
  font-family: 'SimSun', serif;
}

.demo-style {
  color: rgba(255, 255, 255, 0.5);
  font-size: 1rem;
}

/* 设计历史 */
.design-history {
  padding: 60px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.design-history h2 {
  color: #ffffff;
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 30px 0;
  text-align: center;
}

.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.history-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.history-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
}

.history-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.history-info {
  padding: 12px;
}

.history-info .content {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffd700;
  font-family: 'SimSun', serif;
  margin-bottom: 4px;
}

.history-info .style {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4px;
}

.history-info .date {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

/* 积分对话框 */
.credit-dialog-content {
  text-align: center;
  padding: 20px;
}

.credit-dialog-content p {
  margin: 12px 0;
  color: #666;
}

/* 表单样式覆盖 */
:deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

:deep(.el-card__header) {
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.el-card__body) {
  background: transparent;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-radio__label) {
  color: rgba(255, 255, 255, 0.8);
}

:deep(.el-progress-bar__outer) {
  background: rgba(255, 255, 255, 0.2);
}

:deep(.el-progress-bar__inner) {
  background: linear-gradient(90deg, #d4af37, #ffd700);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .workspace-grid {
    grid-template-columns: 350px 1fr;
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .workspace-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .design-preview {
    min-height: 400px;
  }

  .chinese-char-animation {
    font-size: 4rem;
  }

  .demo-character {
    font-size: 3rem;
  }

  .history-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .image-actions {
    flex-direction: column;
  }

  .image-meta {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
