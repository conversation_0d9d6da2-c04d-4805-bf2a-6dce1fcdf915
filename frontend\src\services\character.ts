// 汉字成语服务
import api from './api'
import type {
  SearchResponse,
  ChineseCharacter,
  ChineseIdiom,
  RecommendationRequest,
  RecommendationResponse
} from '@/types'

export class CharacterService {
  // 搜索汉字和成语
  static async search(params: {
    q: string
    type?: string
    limit?: number
  }): Promise<SearchResponse> {
    const response = await api.get<SearchResponse>('/characters/search', {
      params
    })
    return response.data
  }

  // 获取汉字详情
  static async getCharacterDetail(id: number): Promise<ChineseCharacter> {
    const response = await api.get<ChineseCharacter>(`/characters/characters/${id}`)
    return response.data
  }

  // 获取成语详情
  static async getIdiomDetail(id: number): Promise<ChineseIdiom> {
    const response = await api.get<ChineseIdiom>(`/characters/idioms/${id}`)
    return response.data
  }

  // 获取AI推荐 - 使用更长的超时时间适应思维型模型
  static async getRecommendations(request: RecommendationRequest): Promise<RecommendationResponse> {
    const response = await api.post<RecommendationResponse>('/characters/recommend', request, {
      timeout: 180000 // 3分钟超时，专门为AI推荐设置
    })
    return response.data
  }

  // 获取热门内容
  static async getPopular(params: {
    type?: string
    limit?: number
  }): Promise<{
    characters?: ChineseCharacter[]
    idioms?: ChineseIdiom[]
  }> {
    const response = await api.get('/characters/popular', {
      params
    })
    return response.data
  }
}
