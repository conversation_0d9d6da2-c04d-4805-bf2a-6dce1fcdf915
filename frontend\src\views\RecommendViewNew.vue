<template>
  <div class="recommend-page">
    <!-- 页面头部 -->
    <section class="recommend-header">
      <div class="container">
        <div class="header-content">
          <h1>AI纹身推荐</h1>
          <p>告诉我们您的想法，AI为您推荐最合适的汉字和成语，并生成纹身设计</p>
        </div>
      </div>
    </section>

    <!-- 输入区域 -->
    <section class="input-section">
      <div class="container">
        <el-card class="input-card">
          <div class="input-form">
            <el-input
              v-model="userInput"
              type="textarea"
              :rows="4"
              placeholder="请详细描述您希望纹身表达的含义、情感或故事背景...&#10;例如：我想要一个代表坚强和勇气的纹身，能够激励我在困难时不放弃"
              maxlength="500"
              show-word-limit
              class="user-input"
            />
            <div class="input-actions">
              <el-button
                type="primary"
                size="large"
                @click="getAIRecommendations"
                :loading="isLoading"
                :disabled="!userInput.trim()"
              >
                <el-icon><MagicStick /></el-icon>
                {{ isLoading ? 'AI正在深度思考中...' : '获取AI推荐' }}
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </section>

    <!-- AI推荐结果 -->
    <section class="recommendations-section" v-if="aiRecommendations.length > 0">
      <div class="container">
        <div class="results-header">
          <h2>AI推荐结果</h2>
          <p>{{ aiExplanation }}</p>
        </div>

        <!-- 推荐的汉字/成语选择 -->
        <div class="recommendations-grid">
          <div
            v-for="(item, index) in aiRecommendations"
            :key="index"
            class="recommendation-item"
          >
            <div class="item-content">
              <div class="item-text">{{ item.content }}</div>
              <div class="item-info">
                <div class="pinyin">{{ item.pinyin }}</div>
                <div class="meaning">{{ item.meaning }}</div>
                <div class="explanation">{{ item.explanation }}</div>
              </div>
            </div>
            <div class="item-actions">
              <el-button 
                type="primary" 
                @click="generateTattooDesign(item)"
                :loading="item.generating"
                :disabled="item.generating"
              >
                <el-icon><Picture /></el-icon>
                生成纹身图
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 生成的纹身图片列表 -->
    <section class="tattoo-gallery" v-if="tattooImages.length > 0">
      <div class="container">
        <div class="gallery-header">
          <h2>生成的纹身设计</h2>
          <p>点击图片查看大图，满意的设计可以支付下载高清版</p>
        </div>

        <!-- 图片网格 -->
        <div class="images-grid">
          <div
            v-for="(image, index) in tattooImages"
            :key="image.id"
            class="image-item"
          >
            <div class="image-container">
              <img :src="image.thumbnail_url || image.image_url" :alt="image.content" />
              <div class="image-overlay">
                <div class="image-info">
                  <div class="content">{{ image.content }}</div>
                  <div class="style">{{ image.style }}</div>
                </div>
                <div class="image-actions">
                  <el-button size="small" @click="previewImage(image)">
                    <el-icon><View /></el-icon>
                    预览
                  </el-button>
                  <el-button 
                    type="primary" 
                    size="small" 
                    @click="purchaseHighRes(image)"
                    v-if="image.is_watermarked"
                  >
                    <el-icon><Download /></el-icon>
                    购买高清
                  </el-button>
                  <el-button 
                    type="success" 
                    size="small" 
                    @click="downloadImage(image)"
                    v-else
                  >
                    <el-icon><Download /></el-icon>
                    下载
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { MagicStick, Picture, View, Download } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/counter'
import { CharacterService } from '@/services/character'
import { TattooService } from '@/services/tattoo'

const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const userInput = ref('')
const isLoading = ref(false)
const aiRecommendations = ref<any[]>([])
const aiExplanation = ref('')
const tattooImages = ref<any[]>([])


onMounted(() => {
  // 检查用户登录状态
  if (!userStore.isAuthenticated) {
    ElMessage.warning('请先登录')
    router.push('/login')
  }
})

// 获取AI推荐
const getAIRecommendations = async () => {
  if (!userInput.value.trim()) return

  isLoading.value = true

  // 显示友好的加载提示
  ElMessage.info({
    message: 'AI正在深度思考中，请耐心等待...',
    duration: 3000
  })

  try {
    // 调用AI推荐API
    const result = await CharacterService.getRecommendations({
      keywords: [],
      description: userInput.value,
      type: 'both',
      max_results: 5
    })

    // 处理推荐结果
    aiRecommendations.value = [
      ...result.characters.map((char: any) => ({
        content: char.character,
        pinyin: char.pinyin,
        meaning: char.meaning_en,
        explanation: char.explanation,
        type: 'character',
        generating: false
      })),
      ...result.idioms.map((idiom: any) => ({
        content: idiom.idiom,
        pinyin: idiom.pinyin,
        meaning: idiom.meaning_en,
        explanation: idiom.explanation,
        type: 'idiom',
        generating: false
      }))
    ]

    aiExplanation.value = result.ai_explanation

    ElMessage.success('AI推荐获取成功！')

  } catch (error) {
    console.error('获取推荐失败:', error)
    ElMessage.error('获取推荐失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 生成纹身设计
const generateTattooDesign = async (item: any) => {
  item.generating = true

  try {
    // 调用纹身生成API
    const result = await TattooService.generateTattooImage({
      content: item.content,
      style: 'calligraphy',
      position: 'forearm',
      high_resolution: false
    })

    // 添加到图片列表
    tattooImages.value.unshift({
      ...result,
      content: item.content,
      style: '书法体',
      is_watermarked: true
    })

    ElMessage.success('纹身设计生成成功！')

  } catch (error) {
    console.error('生成纹身设计失败:', error)
    ElMessage.error('生成失败，请稍后重试')
  } finally {
    item.generating = false
  }
}

// 预览图片
const previewImage = (image: any) => {
  window.open(image.image_url, '_blank')
}

// 购买高清版
const purchaseHighRes = async (image: any) => {
  try {
    const result = await TattooService.unlockHighResolution(image.id)

    // 更新图片信息
    const index = tattooImages.value.findIndex(img => img.id === image.id)
    if (index !== -1) {
      tattooImages.value[index] = { ...result, is_watermarked: false }
    }

    ElMessage.success('高清版本解锁成功！')

  } catch (error) {
    console.error('购买高清版失败:', error)
    ElMessage.error('购买失败，请稍后重试')
  }
}

// 下载图片
const downloadImage = (image: any) => {
  const link = document.createElement('a')
  link.href = image.image_url
  link.download = `tattoo-${image.content}-${Date.now()}.png`
  link.click()
}


</script>

<style scoped>
.recommend-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 页面头部 */
.recommend-header {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-content {
  text-align: center;
}

.header-content h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
  font-family: 'SimSun', serif;
}

.header-content p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
}

/* 输入区域 */
.input-section {
  padding: 40px 0;
}

.input-card {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-input {
  font-size: 1.1rem;
}

:deep(.user-input .el-textarea__inner) {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
  font-size: 1.1rem;
  line-height: 1.6;
}

.input-actions {
  text-align: center;
}

/* 推荐结果区域 */
.recommendations-section {
  padding: 40px 0;
}

.results-header {
  text-align: center;
  margin-bottom: 40px;
}

.results-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.results-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 215, 0, 0.1);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.recommendation-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
}

.item-text {
  font-size: 3rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  text-align: center;
  margin-bottom: 16px;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.item-info {
  text-align: center;
  margin-bottom: 20px;
}

.pinyin {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  font-style: italic;
}

.meaning {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
  font-weight: 500;
}

.explanation {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.item-actions {
  text-align: center;
}

/* 纹身图片画廊 */
.tattoo-gallery {
  padding: 60px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.gallery-header {
  text-align: center;
  margin-bottom: 40px;
}

.gallery-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.gallery-header p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.image-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.image-item:hover {
  transform: translateY(-5px);
  border-color: rgba(255, 215, 0, 0.3);
}

.image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.8) 100%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.image-info {
  color: #ffffff;
}

.image-info .content {
  font-size: 1.2rem;
  font-weight: 600;
  font-family: 'SimSun', serif;
  margin-bottom: 4px;
}

.image-info .style {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.image-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}



/* 表单样式覆盖 */
:deep(.el-card__body) {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content h1 {
    font-size: 2rem;
  }

  .recommendations-grid {
    grid-template-columns: 1fr;
  }

  .images-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .item-text {
    font-size: 2.5rem;
  }

  .input-card {
    margin: 0 16px;
  }
}
</style>
