"""
用户相关数据模式
User Related Data Schemas
"""

from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    """用户基础模式"""
    email: EmailStr
    username: str
    full_name: Optional[str] = None
    language_preference: str = "en"

class UserCreate(UserBase):
    """用户创建模式"""
    password: str

class UserUpdate(BaseModel):
    """用户更新模式"""
    full_name: Optional[str] = None
    bio: Optional[str] = None
    language_preference: Optional[str] = None
    avatar_url: Optional[str] = None

class UserResponse(UserBase):
    """用户响应模式"""
    id: int
    is_active: bool
    is_verified: bool
    is_premium: bool
    credits: int
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    created_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True

class UserLogin(BaseModel):
    """用户登录模式"""
    username: str
    password: str
