#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本AI服务 - 可配置的AI模型服务
支持DeepSeek、OpenAI等多种文本AI模型
"""

import json
import httpx
from typing import Dict, Any
from app.core.config import settings

class TextAIService:
    """文本AI服务类 - 可配置不同的AI模型"""
    
    def __init__(self):
        self.api_key = settings.TEXT_AI_API_KEY
        self.base_url = settings.TEXT_AI_API_URL
        self.model = settings.TEXT_AI_MODEL
    
    async def recommend_characters(self, user_description: str, max_results: int = 5) -> Dict[str, Any]:
        """
        根据用户描述推荐汉字和成语
        
        Args:
            user_description: 用户的描述和想法
            max_results: 最大推荐数量
            
        Returns:
            包含推荐结果的字典
        """
        
        # 构建提示词
        prompt = self._build_recommendation_prompt(user_description, max_results)
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": self.model,
                        "messages": [
                            {
                                "role": "system",
                                "content": "你是一个专业的汉字文化专家，擅长根据用户的想法和情感需求推荐最合适的汉字和成语。你的推荐要考虑文化内涵、寓意深度和纹身适用性。"
                            },
                            {
                                "role": "user", 
                                "content": prompt
                            }
                        ],
                        "temperature": 0.7,
                        "max_tokens": 2000,
                        "stream": False
                    },
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    
                    # 解析AI返回的JSON结果
                    try:
                        recommendations = json.loads(content)
                        return recommendations
                    except json.JSONDecodeError:
                        # 如果JSON解析失败，返回默认结果
                        return self._get_fallback_recommendations(user_description)
                        
                else:
                    print(f"文本AI API错误: {response.status_code} - {response.text}")
                    return self._get_fallback_recommendations(user_description)
                    
        except Exception as e:
            print(f"调用文本AI API失败: {e}")
            return self._get_fallback_recommendations(user_description)
    
    def _build_recommendation_prompt(self, user_description: str, max_results: int) -> str:
        """构建推荐提示词"""
        
        prompt = f"""
请根据用户的描述推荐最合适的汉字和成语用于纹身设计。

用户描述："{user_description}"

请推荐{max_results}个汉字和{max_results}个成语，要求：

1. 汉字要求：
   - 单个汉字，寓意深刻
   - 适合纹身设计（字形美观）
   - 与用户描述的情感和含义高度匹配
   - 具有积极正面的文化内涵

2. 成语要求：
   - 4字成语为主
   - 朗朗上口，寓意深远
   - 与用户需求高度相关
   - 有典故来源更佳

3. 输出格式：
请严格按照以下JSON格式返回，不要添加任何其他文字：

{{
    "ai_explanation": "根据您的描述，我为您推荐以下汉字和成语...",
    "characters": [
        {{
            "character": "智",
            "pinyin": "zhì",
            "meaning_en": "wisdom, intelligence",
            "meaning_zh": "智慧，聪明",
            "explanation": "为什么推荐这个汉字的详细解释",
            "cultural_context": "文化背景和典故",
            "score": 9.5
        }}
    ],
    "idioms": [
        {{
            "idiom": "自强不息",
            "pinyin": "zì qiáng bù xī",
            "meaning_en": "constantly strive to become stronger",
            "meaning_zh": "自己努力向上，永不懈怠",
            "explanation": "为什么推荐这个成语的详细解释",
            "origin_story": "典故来源",
            "character_count": 4,
            "score": 9.2
        }}
    ]
}}

请确保推荐的内容与用户描述高度相关，并提供详细的文化解释。
"""
        return prompt
    
    def _get_fallback_recommendations(self, user_description: str) -> Dict[str, Any]:
        """当API调用失败时的备用推荐"""
        
        # 根据关键词提供基础推荐
        fallback_data = {
            "ai_explanation": f"根据您的描述「{user_description}」，我为您推荐以下具有深刻文化内涵的汉字和成语。这些推荐都蕴含着积极向上的寓意，适合作为纹身设计。",
            "characters": [
                {
                    "character": "智",
                    "pinyin": "zhì",
                    "meaning_en": "wisdom, intelligence",
                    "meaning_zh": "智慧，聪明",
                    "explanation": "智慧是人生最宝贵的财富，代表着理性思考和明智决策的能力。",
                    "cultural_context": "在中华文化中，智慧被视为君子必备的品德之一。",
                    "score": 9.0
                },
                {
                    "character": "勇",
                    "pinyin": "yǒng",
                    "meaning_en": "courage, bravery",
                    "meaning_zh": "勇敢，勇气",
                    "explanation": "勇气是面对困难和挑战时的坚定意志，是成功的重要品质。",
                    "cultural_context": "勇者无畏，是中华民族崇尚的精神品质。",
                    "score": 8.8
                },
                {
                    "character": "爱",
                    "pinyin": "ài",
                    "meaning_en": "love, affection",
                    "meaning_zh": "爱情，喜爱",
                    "explanation": "爱是人类最基本的情感，代表着关怀、温暖和奉献。",
                    "cultural_context": "爱是连接人与人之间最美好的纽带。",
                    "score": 9.2
                }
            ],
            "idioms": [
                {
                    "idiom": "自强不息",
                    "pinyin": "zì qiáng bù xī",
                    "meaning_en": "constantly strive to become stronger",
                    "meaning_zh": "自己努力向上，永不懈怠",
                    "explanation": "这个成语体现了永不放弃、持续进步的精神品质。",
                    "origin_story": "出自《周易》，象征着永不放弃的精神品质。",
                    "character_count": 4,
                    "score": 9.5
                },
                {
                    "idiom": "厚德载物",
                    "pinyin": "hòu dé zài wù",
                    "meaning_en": "virtue carries all things",
                    "meaning_zh": "以深厚的德泽育人利物",
                    "explanation": "强调品德修养的重要性，德行深厚才能承载万物。",
                    "origin_story": "出自《周易》，强调品德修养的重要性。",
                    "character_count": 4,
                    "score": 9.0
                }
            ]
        }
        
        return fallback_data
