#!/usr/bin/env python3
"""
数据库初始化脚本
Database Initialization Script
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.db.database import Base
from app.db.init_data import initialize_database

# 导入所有模型以确保它们被注册
from app.models.user import User
from app.models.character import ChineseCharacter, ChineseIdiom
from app.models.tattoo import TattooRequest, TattooImage, TattooStyle

def create_tables():
    """创建数据库表"""
    print("正在创建数据库表...")
    
    # 创建数据库引擎
    engine = create_engine(settings.DATABASE_URL, echo=True)
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    print("✓ 数据库表创建完成")
    
    return engine

def init_sample_data():
    """初始化示例数据"""
    print("正在初始化示例数据...")
    
    # 创建数据库引擎和会话
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    db = SessionLocal()
    
    try:
        # 初始化数据
        initialize_database(db)
        print("✓ 示例数据初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化示例数据失败: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """主函数"""
    print("=== 汉字纹身AI平台数据库初始化 ===")
    
    try:
        # 创建数据库表
        engine = create_tables()
        
        # 初始化示例数据
        init_sample_data()
        
        print("\n🎉 数据库初始化成功完成！")
        print("\n可以使用以下命令启动应用：")
        print("uvicorn main:app --reload")
        
    except Exception as e:
        print(f"\n❌ 数据库初始化失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
