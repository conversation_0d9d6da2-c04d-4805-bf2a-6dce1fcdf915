#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试图像生成服务
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.image_generation_service import ImageGenerationService

async def test_simple():
    """简单测试"""
    
    print("🔍 简单测试图像生成...")
    
    image_service = ImageGenerationService()
    
    print(f"🔧 API URL: {image_service.image_api_url}")
    print(f"🔧 API Key: {image_service.image_api_key[:20]}...")
    print(f"🔧 Model: {image_service.image_model}")
    
    prompt = "智"
    
    try:
        print(f"🚀 生成图片: {prompt}")
        result = await image_service.generate_tattoo_image(prompt, False)
        
        print("✅ 成功!")
        print(f"   URL: {result.get('image_url')}")
        print(f"   Model: {result.get('model')}")
        
    except Exception as e:
        print(f"❌ 失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_simple())
