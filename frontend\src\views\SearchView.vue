<template>
  <div class="search-page">
    <!-- 搜索头部区域 -->
    <section class="search-header">
      <div class="container">
        <div class="search-intro">
          <h1>探索汉字的文化内涵</h1>
          <p>搜索数千个汉字和成语，了解它们的深层含义和纹身适用性</p>
        </div>
        
        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchQuery"
            size="large"
            placeholder="输入汉字、成语、拼音或英文含义..."
            class="search-input"
            @keyup.enter="handleSearch"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button 
                type="primary" 
                @click="handleSearch"
                :loading="isSearching"
              >
                搜索
              </el-button>
            </template>
          </el-input>
        </div>

        <!-- 搜索过滤器 -->
        <div class="search-filters">
          <el-radio-group v-model="searchType" @change="handleSearch">
            <el-radio-button label="both">全部</el-radio-button>
            <el-radio-button label="character">汉字</el-radio-button>
            <el-radio-button label="idiom">成语</el-radio-button>
          </el-radio-group>
          
          <el-select 
            v-model="sortBy" 
            placeholder="排序方式"
            @change="handleSearch"
            style="width: 150px"
          >
            <el-option label="相关度" value="relevance" />
            <el-option label="流行度" value="popularity" />
            <el-option label="字母顺序" value="alphabetical" />
          </el-select>
        </div>
      </div>
    </section>

    <!-- 搜索结果区域 -->
    <section class="search-results" v-if="hasSearched">
      <div class="container">
        <!-- 结果统计 -->
        <div class="results-header">
          <h2>搜索结果</h2>
          <p v-if="searchResults">
            找到 {{ searchResults.total }} 个结果
            <span v-if="searchQuery">关于 "{{ searchQuery }}"</span>
          </p>
        </div>

        <!-- 加载状态 -->
        <div v-if="isSearching" class="loading-container">
          <el-skeleton :rows="6" animated />
        </div>

        <!-- 搜索结果内容 -->
        <div v-else-if="searchResults" class="results-content">
          <!-- 汉字结果 -->
          <div v-if="searchResults.characters.length > 0" class="results-section">
            <h3 class="section-title">
              <el-icon><Document /></el-icon>
              汉字 ({{ searchResults.characters.length }})
            </h3>
            <div class="characters-grid">
              <div 
                v-for="character in searchResults.characters" 
                :key="character.id"
                class="character-card"
                @click="handleCharacterClick(character)"
              >
                <div class="character-main">{{ character.character }}</div>
                <div class="character-info">
                  <div class="pinyin">{{ character.pinyin }}</div>
                  <div class="meaning-en">{{ character.meaning_en }}</div>
                  <div class="meaning-zh">{{ character.meaning_zh }}</div>
                  <div class="meta-info">
                    <span class="category" v-if="character.category">
                      {{ character.category }}
                    </span>
                    <span class="popularity">
                      <el-icon><Star /></el-icon>
                      {{ character.tattoo_popularity }}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 成语结果 -->
          <div v-if="searchResults.idioms.length > 0" class="results-section">
            <h3 class="section-title">
              <el-icon><Collection /></el-icon>
              成语 ({{ searchResults.idioms.length }})
            </h3>
            <div class="idioms-grid">
              <div 
                v-for="idiom in searchResults.idioms" 
                :key="idiom.id"
                class="idiom-card"
                @click="handleIdiomClick(idiom)"
              >
                <div class="idiom-main">{{ idiom.idiom }}</div>
                <div class="idiom-info">
                  <div class="pinyin">{{ idiom.pinyin }}</div>
                  <div class="meaning-en">{{ idiom.meaning_en }}</div>
                  <div class="meaning-zh">{{ idiom.meaning_zh }}</div>
                  <div class="meta-info">
                    <span class="character-count">{{ idiom.character_count }}字</span>
                    <span class="difficulty">难度: {{ idiom.difficulty_level }}/5</span>
                    <span class="popularity">
                      <el-icon><Star /></el-icon>
                      {{ idiom.tattoo_popularity }}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 无结果状态 -->
          <div v-if="searchResults.total === 0" class="no-results">
            <div class="no-results-icon">
              <el-icon size="64"><Search /></el-icon>
            </div>
            <h3>未找到相关结果</h3>
            <p>尝试使用不同的关键词或检查拼写</p>
            <el-button type="primary" @click="clearSearch">
              清除搜索
            </el-button>
          </div>
        </div>
      </div>
    </section>

    <!-- 热门推荐区域 -->
    <section class="popular-recommendations" v-if="!hasSearched">
      <div class="container">
        <div class="section-header">
          <h2>热门推荐</h2>
          <p>最受欢迎的纹身汉字和成语</p>
        </div>
        
        <div class="recommendations-tabs">
          <el-tabs v-model="activeTab" @tab-change="loadPopularContent">
            <el-tab-pane label="热门汉字" name="characters">
              <div class="characters-grid" v-if="popularCharacters.length > 0">
                <div 
                  v-for="character in popularCharacters" 
                  :key="character.id"
                  class="character-card"
                  @click="handleCharacterClick(character)"
                >
                  <div class="character-main">{{ character.character }}</div>
                  <div class="character-info">
                    <div class="pinyin">{{ character.pinyin }}</div>
                    <div class="meaning-en">{{ character.meaning_en }}</div>
                    <div class="popularity">
                      <el-icon><Star /></el-icon>
                      {{ character.tattoo_popularity }}%
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="热门成语" name="idioms">
              <div class="idioms-grid" v-if="popularIdioms.length > 0">
                <div 
                  v-for="idiom in popularIdioms" 
                  :key="idiom.id"
                  class="idiom-card"
                  @click="handleIdiomClick(idiom)"
                >
                  <div class="idiom-main">{{ idiom.idiom }}</div>
                  <div class="idiom-info">
                    <div class="pinyin">{{ idiom.pinyin }}</div>
                    <div class="meaning-en">{{ idiom.meaning_en }}</div>
                    <div class="popularity">
                      <el-icon><Star /></el-icon>
                      {{ idiom.tattoo_popularity }}%
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, Document, Collection, Star } from '@element-plus/icons-vue'
import type { SearchResponse, ChineseCharacter, ChineseIdiom } from '@/types'
import { CharacterService } from '@/services/character'

const route = useRoute()
const router = useRouter()

// 搜索状态
const searchQuery = ref('')
const searchType = ref('both')
const sortBy = ref('relevance')
const isSearching = ref(false)
const hasSearched = ref(false)
const searchResults = ref<SearchResponse | null>(null)

// 热门内容
const activeTab = ref('characters')
const popularCharacters = ref<ChineseCharacter[]>([])
const popularIdioms = ref<ChineseIdiom[]>([])

// 初始化
onMounted(() => {
  // 从URL参数获取搜索词
  const query = route.query.q as string
  if (query) {
    searchQuery.value = query
    handleSearch()
  } else {
    loadPopularContent()
  }
})

// 监听路由变化
watch(() => route.query.q, (newQuery) => {
  if (newQuery && newQuery !== searchQuery.value) {
    searchQuery.value = newQuery as string
    handleSearch()
  }
})

// 执行搜索
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  isSearching.value = true
  hasSearched.value = true

  try {
    const results = await CharacterService.search({
      q: searchQuery.value,
      type: searchType.value,
      limit: 20
    })
    
    searchResults.value = results
    
    // 更新URL
    router.replace({ 
      query: { 
        ...route.query, 
        q: searchQuery.value,
        type: searchType.value !== 'both' ? searchType.value : undefined
      } 
    })
    
  } catch (error) {
    ElMessage.error('搜索失败，请稍后重试')
    console.error('搜索错误:', error)
  } finally {
    isSearching.value = false
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  hasSearched.value = false
  searchResults.value = null
  router.replace({ query: {} })
}

// 加载热门内容
const loadPopularContent = async () => {
  try {
    const popular = await CharacterService.getPopular({
      type: activeTab.value,
      limit: 12
    })
    
    if (activeTab.value === 'characters') {
      popularCharacters.value = popular.characters || []
    } else {
      popularIdioms.value = popular.idioms || []
    }
  } catch (error) {
    console.error('加载热门内容失败:', error)
  }
}

// 处理汉字点击
const handleCharacterClick = (character: ChineseCharacter) => {
  // 这里可以跳转到详情页或者直接进入纹身设计流程
  router.push(`/character/${character.id}`)
}

// 处理成语点击
const handleIdiomClick = (idiom: ChineseIdiom) => {
  // 这里可以跳转到详情页或者直接进入纹身设计流程
  router.push(`/idiom/${idiom.id}`)
}
</script>

<style scoped>
.search-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 搜索头部区域 */
.search-header {
  padding: 60px 0 40px;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.search-intro {
  text-align: center;
  margin-bottom: 40px;
}

.search-intro h1 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
  font-family: 'SimSun', serif;
}

.search-intro p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
}

/* 搜索框 */
.search-box {
  max-width: 800px;
  margin: 0 auto 30px;
}

:deep(.search-input .el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 8px 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

:deep(.search-input .el-input__wrapper:hover) {
  border-color: rgba(255, 215, 0, 0.5);
}

:deep(.search-input .el-input__wrapper.is-focus) {
  border-color: #ffd700;
  box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
}

:deep(.search-input .el-input__inner) {
  color: #ffffff;
  font-size: 16px;
}

:deep(.search-input .el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.5);
}

:deep(.search-input .el-input-group__append .el-button) {
  background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
  border: none;
  color: #1a1a1a;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 0 20px 20px 0;
}

/* 搜索过滤器 */
.search-filters {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

:deep(.el-radio-group .el-radio-button__inner) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

:deep(.el-radio-group .el-radio-button__inner:hover) {
  background: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
  color: #ffd700;
}

:deep(.el-radio-group .el-radio-button.is-active .el-radio-button__inner) {
  background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
  border-color: #ffd700;
  color: #1a1a1a;
}

/* 搜索结果区域 */
.search-results {
  padding: 40px 0;
}

.results-header {
  margin-bottom: 30px;
}

.results-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
}

.results-header p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1rem;
}

.loading-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 结果区域 */
.results-section {
  margin-bottom: 50px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffd700;
  margin: 0 0 24px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(255, 215, 0, 0.3);
}

/* 汉字网格 */
.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.character-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.character-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.character-main {
  font-size: 3rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  text-align: center;
  margin-bottom: 16px;
  text-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.character-info .pinyin {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 8px;
  font-style: italic;
}

.character-info .meaning-en {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-bottom: 6px;
  font-weight: 500;
}

.character-info .meaning-zh {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 12px;
}

.meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  font-size: 0.85rem;
}

.category {
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.popularity {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #ffd700;
  font-weight: 600;
}

/* 成语网格 */
.idioms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.idiom-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.idiom-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.idiom-main {
  font-size: 2rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  text-align: center;
  margin-bottom: 16px;
  text-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.idiom-info .pinyin {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 8px;
  font-style: italic;
}

.idiom-info .meaning-en {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin-bottom: 6px;
  font-weight: 500;
}

.idiom-info .meaning-zh {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-bottom: 12px;
}

.character-count,
.difficulty {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

/* 无结果状态 */
.no-results {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.6);
}

.no-results-icon {
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-results h3 {
  font-size: 1.5rem;
  margin: 0 0 12px 0;
  color: rgba(255, 255, 255, 0.8);
}

.no-results p {
  margin-bottom: 24px;
}

/* 热门推荐区域 */
.popular-recommendations {
  padding: 60px 0;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.section-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
}

.recommendations-tabs {
  max-width: 1000px;
  margin: 0 auto;
}

:deep(.el-tabs__header) {
  margin-bottom: 30px;
}

:deep(.el-tabs__nav-wrap::after) {
  background: rgba(255, 255, 255, 0.1);
}

:deep(.el-tabs__item) {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.1rem;
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: #ffd700;
}

:deep(.el-tabs__active-bar) {
  background: #ffd700;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-intro h1 {
    font-size: 2rem;
  }

  .search-filters {
    flex-direction: column;
    gap: 16px;
  }

  .characters-grid,
  .idioms-grid {
    grid-template-columns: 1fr;
  }

  .character-main {
    font-size: 2.5rem;
  }

  .idiom-main {
    font-size: 1.5rem;
  }

  .meta-info {
    justify-content: center;
  }
}
</style>
