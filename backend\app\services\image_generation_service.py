"""
图像生成服务
Image Generation Service using AI APIs
"""

import os
import uuid
import requests
import base64
from typing import Dict, Any, Optional
from PIL import Image, ImageDraw, ImageFont
import io
from app.core.config import settings

class ImageGenerationService:
    """图像生成服务类"""
    
    def __init__(self):
        self.stability_api_key = settings.STABILITY_AI_API_KEY
        self.upload_dir = settings.UPLOAD_DIR
        
        # 确保上传目录存在
        os.makedirs(self.upload_dir, exist_ok=True)
    
    async def generate_tattoo_image(
        self, 
        prompt: str, 
        high_resolution: bool = False
    ) -> Dict[str, Any]:
        """
        生成纹身图片
        
        Args:
            prompt: 生成提示词
            high_resolution: 是否生成高分辨率图片
            
        Returns:
            包含图片信息的字典
        """
        
        try:
            if self.stability_api_key:
                # 使用Stability AI API
                return await self._generate_with_stability_ai(prompt, high_resolution)
            else:
                # 使用模拟生成（开发环境）
                return await self._generate_mock_image(prompt, high_resolution)
                
        except Exception as e:
            # 如果AI生成失败，返回模拟图片
            print(f"图像生成失败，使用模拟图片: {str(e)}")
            return await self._generate_mock_image(prompt, high_resolution)
    
    async def _generate_with_stability_ai(
        self, 
        prompt: str, 
        high_resolution: bool
    ) -> Dict[str, Any]:
        """使用Stability AI生成图片"""
        
        # Stability AI API配置
        api_url = "https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image"
        
        # 根据分辨率设置参数
        width = 1024 if high_resolution else 512
        height = 1024 if high_resolution else 512
        
        headers = {
            "Authorization": f"Bearer {self.stability_api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        data = {
            "text_prompts": [
                {
                    "text": prompt,
                    "weight": 1
                },
                {
                    "text": "blurry, bad quality, distorted, ugly, low resolution",
                    "weight": -1
                }
            ],
            "cfg_scale": 7,
            "height": height,
            "width": width,
            "samples": 1,
            "steps": 30,
            "style_preset": "enhance"
        }
        
        response = requests.post(api_url, headers=headers, json=data)
        
        if response.status_code == 200:
            result = response.json()
            
            # 保存生成的图片
            image_data = base64.b64decode(result["artifacts"][0]["base64"])
            image_filename = f"tattoo_{uuid.uuid4().hex}.png"
            image_path = os.path.join(self.upload_dir, image_filename)
            
            with open(image_path, "wb") as f:
                f.write(image_data)
            
            # 生成缩略图
            thumbnail_path = await self._create_thumbnail(image_path)
            
            # 添加水印（如果不是高分辨率）
            watermarked_path = None
            if not high_resolution:
                watermarked_path = await self._add_watermark(image_path)
            
            return {
                "image_url": f"/uploads/{image_filename}",
                "thumbnail_url": f"/uploads/{os.path.basename(thumbnail_path)}" if thumbnail_path else None,
                "watermarked_url": f"/uploads/{os.path.basename(watermarked_path)}" if watermarked_path else None,
                "width": width,
                "height": height,
                "file_size": os.path.getsize(image_path),
                "model": "stable-diffusion-xl",
                "quality_score": 0.9
            }
        else:
            raise Exception(f"Stability AI API错误: {response.status_code} - {response.text}")
    
    async def _generate_mock_image(
        self, 
        prompt: str, 
        high_resolution: bool
    ) -> Dict[str, Any]:
        """生成模拟图片（用于开发和测试）"""
        
        # 创建模拟图片
        width = 1024 if high_resolution else 512
        height = 1024 if high_resolution else 512
        
        # 创建白色背景图片
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        # 尝试加载字体
        try:
            # 在Windows上尝试使用系统字体
            font_size = 72 if high_resolution else 36
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            # 如果找不到字体，使用默认字体
            font = ImageFont.load_default()
        
        # 从提示词中提取汉字（简单提取）
        chinese_chars = ""
        for char in prompt:
            if '\u4e00' <= char <= '\u9fff':  # 中文字符范围
                chinese_chars += char
        
        if not chinese_chars:
            chinese_chars = "智"  # 默认字符
        
        # 在图片中心绘制汉字
        text_bbox = draw.textbbox((0, 0), chinese_chars, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        draw.text((x, y), chinese_chars, fill='black', font=font)
        
        # 保存图片
        image_filename = f"mock_tattoo_{uuid.uuid4().hex}.png"
        image_path = os.path.join(self.upload_dir, image_filename)
        image.save(image_path, 'PNG')
        
        # 生成缩略图
        thumbnail_path = await self._create_thumbnail(image_path)
        
        # 添加水印（如果不是高分辨率）
        watermarked_path = None
        if not high_resolution:
            watermarked_path = await self._add_watermark(image_path)
        
        return {
            "image_url": f"/uploads/{image_filename}",
            "thumbnail_url": f"/uploads/{os.path.basename(thumbnail_path)}" if thumbnail_path else None,
            "watermarked_url": f"/uploads/{os.path.basename(watermarked_path)}" if watermarked_path else None,
            "width": width,
            "height": height,
            "file_size": os.path.getsize(image_path),
            "model": "mock-generator",
            "quality_score": 0.7
        }
    
    async def _create_thumbnail(self, image_path: str) -> Optional[str]:
        """创建缩略图"""
        try:
            with Image.open(image_path) as img:
                # 创建256x256的缩略图
                img.thumbnail((256, 256), Image.Resampling.LANCZOS)
                
                thumbnail_filename = f"thumb_{os.path.basename(image_path)}"
                thumbnail_path = os.path.join(self.upload_dir, thumbnail_filename)
                
                img.save(thumbnail_path, 'PNG')
                return thumbnail_path
        except Exception as e:
            print(f"创建缩略图失败: {str(e)}")
            return None
    
    async def _add_watermark(self, image_path: str) -> Optional[str]:
        """添加水印"""
        try:
            with Image.open(image_path) as img:
                # 创建水印
                watermark = Image.new('RGBA', img.size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(watermark)
                
                # 水印文字
                watermark_text = "PREVIEW - 墨痕智纹"
                
                try:
                    font = ImageFont.truetype("arial.ttf", 24)
                except:
                    font = ImageFont.load_default()
                
                # 计算水印位置（右下角）
                text_bbox = draw.textbbox((0, 0), watermark_text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                
                x = img.width - text_width - 20
                y = img.height - text_height - 20
                
                # 绘制半透明水印
                draw.text((x, y), watermark_text, fill=(128, 128, 128, 128), font=font)
                
                # 合并图片和水印
                watermarked = Image.alpha_composite(img.convert('RGBA'), watermark)
                
                # 保存带水印的图片
                watermarked_filename = f"watermarked_{os.path.basename(image_path)}"
                watermarked_path = os.path.join(self.upload_dir, watermarked_filename)
                
                watermarked.convert('RGB').save(watermarked_path, 'PNG')
                return watermarked_path
                
        except Exception as e:
            print(f"添加水印失败: {str(e)}")
            return None
