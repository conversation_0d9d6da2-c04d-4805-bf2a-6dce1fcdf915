"""
图像生成服务
Image Generation Service using AI APIs
"""

import os
import uuid
import requests
import base64
from typing import Dict, Any, Optional
from PIL import Image, ImageDraw, ImageFont
import io
from app.core.config import settings


class ImageGenerationService:
    """图像生成服务类"""

    def __init__(self):
        self.image_api_key = settings.IMAGE_AI_API_KEY
        self.image_api_url = settings.IMAGE_AI_API_URL
        self.image_model = settings.IMAGE_AI_MODEL
        self.upload_dir = settings.UPLOAD_DIR

        # 确保上传目录存在
        os.makedirs(self.upload_dir, exist_ok=True)

    async def generate_tattoo_image(self, prompt: str, high_resolution: bool = False) -> Dict[str, Any]:
        """
        生成纹身图片

        Args:
            prompt: 生成提示词
            high_resolution: 是否生成高分辨率图片

        Returns:
            包含图片信息的字典
        """

        try:
            if self.image_api_key:
                # 使用火山引擎 Doubao API
                return await self._generate_with_doubao_ai(prompt, high_resolution)
            else:
                # 使用模拟生成（开发环境）
                return await self._generate_mock_image(prompt, high_resolution)

        except Exception as e:
            # 如果AI生成失败，返回模拟图片
            print(f"图像生成失败，使用模拟图片: {str(e)}")
            return await self._generate_mock_image(prompt, high_resolution)

    async def _generate_with_doubao_ai(self, prompt: str, high_resolution: bool) -> Dict[str, Any]:
        """使用火山引擎Doubao AI生成图片"""

        # 构建专业的纹身设计提示词
        tattoo_prompt = self._build_tattoo_prompt(prompt, high_resolution)

        # 火山引擎API配置
        api_url = f"{self.image_api_url}/images/generations"

        # 根据分辨率设置参数
        size = "1024x1024" if high_resolution else "512x512"

        headers = {"Authorization": f"Bearer {self.image_api_key}", "Content-Type": "application/json"}

        data = {
            "model": self.image_model,
            "prompt": tattoo_prompt,
            "response_format": "url",
            "size": size,
            "guidance_scale": 7.5,
            "watermark": not high_resolution,  # 高分辨率版本不加水印
        }

        response = requests.post(api_url, headers=headers, json=data, timeout=300)  # 5分钟超时

        if response.status_code == 200:
            result = response.json()

            # 获取生成的图片URL
            image_url = result["data"][0]["url"]

            # 下载图片到本地
            image_response = requests.get(image_url, timeout=120)  # 2分钟下载超时
            if image_response.status_code == 200:
                image_filename = f"tattoo_{uuid.uuid4().hex}.png"
                image_path = os.path.join(self.upload_dir, image_filename)

                with open(image_path, "wb") as f:
                    f.write(image_response.content)

                # 生成缩略图
                thumbnail_path = await self._create_thumbnail(image_path)

                # 获取图片尺寸
                width, height = (1024, 1024) if high_resolution else (512, 512)

                return {
                    "image_url": f"/uploads/{image_filename}",
                    "thumbnail_url": f"/uploads/{os.path.basename(thumbnail_path)}" if thumbnail_path else None,
                    "width": width,
                    "height": height,
                    "file_size": os.path.getsize(image_path),
                    "model": self.image_model,
                    "quality_score": 0.9,
                    "is_watermarked": not high_resolution,
                }
            else:
                raise Exception(f"下载生成图片失败: {image_response.status_code}")
        else:
            raise Exception(f"火山引擎API错误: {response.status_code} - {response.text}")

    def _build_tattoo_prompt(self, original_prompt: str, high_resolution: bool) -> str:
        """构建专业的纹身设计提示词"""

        # 基础纹身设计系统提示词
        base_prompt = """Professional Chinese calligraphy tattoo design,
        elegant black ink style, traditional Chinese characters,
        clean white background, minimalist design,
        suitable for tattoo application, high contrast,
        artistic calligraphy strokes, cultural authenticity"""

        # 质量增强词
        quality_terms = "masterpiece, best quality, ultra detailed, sharp focus"

        # 如果是高分辨率，添加更多质量词
        if high_resolution:
            quality_terms += ", ultra high resolution, 4K, professional photography"

        # 组合完整提示词
        full_prompt = f"{base_prompt}, {original_prompt}, {quality_terms}"

        return full_prompt

    async def _generate_mock_image(self, prompt: str, high_resolution: bool) -> Dict[str, Any]:
        """生成模拟图片（用于开发和测试）"""

        # 创建模拟图片
        width = 1024 if high_resolution else 512
        height = 1024 if high_resolution else 512

        # 创建渐变背景图片（从浅灰到白色）
        image = Image.new("RGB", (width, height), "#f8f8f8")
        draw = ImageDraw.Draw(image)

        # 添加渐变背景效果
        for y in range(height):
            color_value = int(248 + (255 - 248) * (y / height))
            color = (color_value, color_value, color_value)
            draw.line([(0, y), (width, y)], fill=color)

        # 尝试加载字体
        try:
            # 在Windows上尝试使用系统字体
            font_size = 120 if high_resolution else 80
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            # 如果找不到字体，使用默认字体
            font = ImageFont.load_default()

        # 从提示词中提取汉字（简单提取）
        chinese_chars = ""
        for char in prompt:
            if "\u4e00" <= char <= "\u9fff":  # 中文字符范围
                chinese_chars += char

        if not chinese_chars:
            chinese_chars = "智"  # 默认字符

        # 在图片中心绘制汉字，使用深蓝色
        text_bbox = draw.textbbox((0, 0), chinese_chars, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]

        x = (width - text_width) // 2
        y = (height - text_height) // 2

        # 添加阴影效果
        shadow_offset = 3
        draw.text((x + shadow_offset, y + shadow_offset), chinese_chars, fill="#cccccc", font=font)

        # 绘制主文字（深蓝色）
        draw.text((x, y), chinese_chars, fill="#1a365d", font=font)

        # 添加装饰边框
        border_color = "#d4af37"  # 金色边框
        border_width = 4
        draw.rectangle(
            [border_width // 2, border_width // 2, width - border_width // 2, height - border_width // 2],
            outline=border_color,
            width=border_width,
        )

        # 保存图片
        image_filename = f"mock_tattoo_{uuid.uuid4().hex}.png"
        image_path = os.path.join(self.upload_dir, image_filename)
        image.save(image_path, "PNG")

        # 生成缩略图
        thumbnail_path = await self._create_thumbnail(image_path)

        # 添加水印（如果不是高分辨率）
        watermarked_path = None
        if not high_resolution:
            watermarked_path = await self._add_watermark(image_path)

        return {
            "image_url": f"/uploads/{image_filename}",
            "thumbnail_url": f"/uploads/{os.path.basename(thumbnail_path)}" if thumbnail_path else None,
            "watermarked_url": f"/uploads/{os.path.basename(watermarked_path)}" if watermarked_path else None,
            "width": width,
            "height": height,
            "file_size": os.path.getsize(image_path),
            "model": "mock-generator",
            "quality_score": 0.7,
        }

    async def _create_thumbnail(self, image_path: str) -> Optional[str]:
        """创建缩略图"""
        try:
            with Image.open(image_path) as img:
                # 确保图片是RGB模式，保持颜色
                if img.mode != "RGB":
                    img = img.convert("RGB")

                # 创建256x256的缩略图，保持高质量
                img.thumbnail((256, 256), Image.Resampling.LANCZOS)

                thumbnail_filename = f"thumb_{os.path.basename(image_path)}"
                thumbnail_path = os.path.join(self.upload_dir, thumbnail_filename)

                # 保存时指定高质量参数
                img.save(thumbnail_path, "PNG", optimize=True, quality=95)
                return thumbnail_path
        except Exception as e:
            print(f"创建缩略图失败: {str(e)}")
            return None

    async def _add_watermark(self, image_path: str) -> Optional[str]:
        """添加水印"""
        try:
            with Image.open(image_path) as img:
                # 创建水印
                watermark = Image.new("RGBA", img.size, (0, 0, 0, 0))
                draw = ImageDraw.Draw(watermark)

                # 水印文字
                watermark_text = "PREVIEW - 墨痕智纹"

                try:
                    font = ImageFont.truetype("arial.ttf", 24)
                except:
                    font = ImageFont.load_default()

                # 计算水印位置（右下角）
                text_bbox = draw.textbbox((0, 0), watermark_text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]

                x = img.width - text_width - 20
                y = img.height - text_height - 20

                # 绘制半透明水印
                draw.text((x, y), watermark_text, fill=(128, 128, 128, 128), font=font)

                # 合并图片和水印
                watermarked = Image.alpha_composite(img.convert("RGBA"), watermark)

                # 保存带水印的图片
                watermarked_filename = f"watermarked_{os.path.basename(image_path)}"
                watermarked_path = os.path.join(self.upload_dir, watermarked_filename)

                # 转换为RGB并保存，保持高质量
                watermarked.convert("RGB").save(watermarked_path, "PNG", optimize=True, quality=95)
                return watermarked_path

        except Exception as e:
            print(f"添加水印失败: {str(e)}")
            return None
