{"version": 3, "file": "trigger2.js", "sources": ["../../../../../../packages/components/tooltip/src/trigger.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { popperTriggerProps } from '@element-plus/components/popper'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\n\nexport type TooltipTriggerType = 'hover' | 'focus' | 'click' | 'contextmenu'\n\nexport const useTooltipTriggerProps = buildProps({\n  ...popperTriggerProps,\n  /**\n   * @description whether Toolt<PERSON> is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description How should the tooltip be triggered (to show)\n   */\n  trigger: {\n    type: definePropType<Arrayable<TooltipTriggerType>>([String, Array]),\n    default: 'hover',\n  },\n  /**\n   * @description When you click the mouse to focus on the trigger element, you can define a set of keyboard codes to control the display of tooltip through the keyboard\n   */\n  triggerKeys: {\n    type: definePropType<string[]>(Array),\n    default: () => [EVENT_CODE.enter, EVENT_CODE.numpadEnter, EVENT_CODE.space],\n  },\n} as const)\n\nexport type ElTooltipTriggerProps = ExtractPropTypes<\n  typeof useTooltipTriggerProps\n>\n"], "names": ["buildProps", "popperTriggerProps", "definePropType", "EVENT_CODE"], "mappings": ";;;;;;;;AAGY,MAAC,sBAAsB,GAAGA,kBAAU,CAAC;AACjD,EAAE,GAAGC,0BAAkB;AACvB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,CAACC,eAAU,CAAC,KAAK,EAAEA,eAAU,CAAC,WAAW,EAAEA,eAAU,CAAC,KAAK,CAAC;AAC/E,GAAG;AACH,CAAC;;;;"}