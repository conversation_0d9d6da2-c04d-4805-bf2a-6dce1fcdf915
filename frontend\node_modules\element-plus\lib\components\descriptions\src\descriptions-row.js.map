{"version": 3, "file": "descriptions-row.js", "sources": ["../../../../../../packages/components/descriptions/src/descriptions-row.vue"], "sourcesContent": ["<template>\n  <template v-if=\"descriptions.direction === 'vertical'\">\n    <tr>\n      <template v-for=\"(cell, _index) in row\" :key=\"`tr1-${_index}`\">\n        <el-descriptions-cell :cell=\"cell\" tag=\"th\" type=\"label\" />\n      </template>\n    </tr>\n    <tr>\n      <template v-for=\"(cell, _index) in row\" :key=\"`tr2-${_index}`\">\n        <el-descriptions-cell :cell=\"cell\" tag=\"td\" type=\"content\" />\n      </template>\n    </tr>\n  </template>\n  <tr v-else>\n    <template v-for=\"(cell, _index) in row\" :key=\"`tr3-${_index}`\">\n      <template v-if=\"descriptions.border\">\n        <el-descriptions-cell :cell=\"cell\" tag=\"td\" type=\"label\" />\n        <el-descriptions-cell :cell=\"cell\" tag=\"td\" type=\"content\" />\n      </template>\n      <el-descriptions-cell v-else :cell=\"cell\" tag=\"td\" type=\"both\" />\n    </template>\n  </tr>\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject } from 'vue'\n\nimport ElDescriptionsCell from './descriptions-cell'\nimport { descriptionsKey } from './token'\nimport { descriptionsRowProps } from './descriptions-row'\n\nimport type { IDescriptionsInject } from './descriptions.type'\n\ndefineOptions({\n  name: 'ElDescriptionsRow',\n})\n\ndefineProps(descriptionsRowProps)\n\nconst descriptions = inject(descriptionsKey, {} as IDescriptionsInject)\n</script>\n"], "names": ["inject", "<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;uCAiCc,CAAA;AAAA,EACZ,IAAM,EAAA,mBAAA;AACR,CAAA,CAAA,CAAA;;;;;AAIA,IAAA,MAAM,YAAe,GAAAA,UAAA,CAAOC,qBAAiB,EAAA,EAAyB,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}