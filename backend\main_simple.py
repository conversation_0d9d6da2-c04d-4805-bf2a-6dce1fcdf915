#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版后端服务 - 无数据库依赖
用于前端测试和开发
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import json
from datetime import datetime, timedelta
import jwt

# 应用配置
app = FastAPI(
    title="Chinese Character Tattoo AI - Simple Version",
    description="汉字纹身AI平台 - 简化版API",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# JWT配置
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"
security = HTTPBearer()

# 数据模型
class User(BaseModel):
    id: int
    email: str
    username: str
    full_name: Optional[str] = None
    is_active: bool = True
    is_premium: bool = False
    credits: int = 10
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    language_preference: str = "zh"

class UserCreate(BaseModel):
    email: str
    username: str
    password: str
    full_name: Optional[str] = None

class UserLogin(BaseModel):
    email: str
    password: str

class ChineseCharacter(BaseModel):
    id: int
    character: str
    pinyin: str
    meaning_en: str
    meaning_zh: str
    cultural_context: Optional[str] = None
    tattoo_popularity: int = 0

class ChineseIdiom(BaseModel):
    idiom: str
    pinyin: str
    meaning_en: str
    meaning_zh: str
    origin_story: Optional[str] = None

class RecommendationRequest(BaseModel):
    keywords: List[str]
    description: str = ""
    type: str = "both"
    max_results: int = 5

class TattooRequest(BaseModel):
    content: str
    style: str
    position: str
    high_resolution: bool = False

# 模拟数据
MOCK_USERS = {}
MOCK_USER_ID_COUNTER = 1

MOCK_CHARACTERS = [
    {
        "id": 1,
        "character": "爱",
        "pinyin": "ài",
        "meaning_en": "love, affection",
        "meaning_zh": "爱情，喜爱",
        "cultural_context": "爱是人类最基本的情感之一，代表着关怀、温暖和奉献。",
        "tattoo_popularity": 95
    },
    {
        "id": 2,
        "character": "智",
        "pinyin": "zhì",
        "meaning_en": "wisdom, intelligence",
        "meaning_zh": "智慧，聪明",
        "cultural_context": "智慧是人生的重要品质，代表着理性思考和明智决策的能力。",
        "tattoo_popularity": 88
    },
    {
        "id": 3,
        "character": "勇",
        "pinyin": "yǒng",
        "meaning_en": "courage, bravery",
        "meaning_zh": "勇敢，勇气",
        "cultural_context": "勇气是面对困难和挑战时的坚定意志，是成功的重要品质。",
        "tattoo_popularity": 92
    },
    {
        "id": 4,
        "character": "和",
        "pinyin": "hé",
        "meaning_en": "harmony, peace",
        "meaning_zh": "和谐，和平",
        "cultural_context": "和谐代表着平衡与统一，是中华文化中的重要理念。",
        "tattoo_popularity": 85
    },
    {
        "id": 5,
        "character": "福",
        "pinyin": "fú",
        "meaning_en": "fortune, blessing",
        "meaning_zh": "福气，幸福",
        "cultural_context": "福是中国文化中最受欢迎的吉祥字，代表着好运和幸福。",
        "tattoo_popularity": 98
    }
]

MOCK_IDIOMS = [
    {
        "idiom": "自强不息",
        "pinyin": "zì qiáng bù xī",
        "meaning_en": "constantly strive to become stronger",
        "meaning_zh": "自己努力向上，永不懈怠",
        "origin_story": "出自《周易》，象征着永不放弃的精神品质。"
    },
    {
        "idiom": "厚德载物",
        "pinyin": "hòu dé zài wù",
        "meaning_en": "virtue carries all things",
        "meaning_zh": "以深厚的德泽育人利物",
        "origin_story": "出自《周易》，强调品德修养的重要性。"
    },
    {
        "idiom": "知行合一",
        "pinyin": "zhī xíng hé yī",
        "meaning_en": "unity of knowledge and action",
        "meaning_zh": "认识与实践的统一",
        "origin_story": "王阳明心学的核心理念，强调理论与实践的结合。"
    }
]

MOCK_STYLES = [
    {
        "name": "calligraphy",
        "name_zh": "书法体",
        "description": "传统中国书法风格，优雅而有力",
        "characteristics": {"stroke": "flowing", "style": "traditional"}
    },
    {
        "name": "modern",
        "name_zh": "现代简约",
        "description": "简洁现代的设计风格",
        "characteristics": {"stroke": "clean", "style": "minimalist"}
    },
    {
        "name": "watercolor",
        "name_zh": "水彩风格",
        "description": "柔和的水彩画效果",
        "characteristics": {"stroke": "soft", "style": "artistic"}
    }
]

# 工具函数
def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(days=30)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: int = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        user = MOCK_USERS.get(user_id)
        if user is None:
            raise HTTPException(status_code=401, detail="User not found")
        return user
    except jwt.PyJWTError:
        raise HTTPException(status_code=401, detail="Invalid token")

# API路由
@app.get("/")
async def root():
    return {"message": "Chinese Character Tattoo AI - Simple Version"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0-simple"}

# 认证相关
@app.post("/api/v1/auth/register")
async def register(user_data: UserCreate):
    global MOCK_USER_ID_COUNTER
    
    # 检查邮箱是否已存在
    for user in MOCK_USERS.values():
        if user["email"] == user_data.email:
            raise HTTPException(status_code=400, detail="Email already registered")
        if user["username"] == user_data.username:
            raise HTTPException(status_code=400, detail="Username already taken")
    
    # 创建新用户
    user_id = MOCK_USER_ID_COUNTER
    MOCK_USER_ID_COUNTER += 1
    
    new_user = {
        "id": user_id,
        "email": user_data.email,
        "username": user_data.username,
        "full_name": user_data.full_name,
        "is_active": True,
        "is_premium": False,
        "credits": 10,
        "avatar_url": None,
        "bio": None,
        "language_preference": "zh"
    }
    
    MOCK_USERS[user_id] = new_user
    
    # 创建访问令牌
    access_token = create_access_token(data={"sub": user_id})
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user": new_user
    }

@app.post("/api/v1/auth/login")
async def login(user_data: UserLogin):
    # 简单的登录验证（实际应用中应该验证密码）
    for user in MOCK_USERS.values():
        if user["email"] == user_data.email:
            access_token = create_access_token(data={"sub": user["id"]})
            return {
                "access_token": access_token,
                "token_type": "bearer",
                "user": user
            }
    
    raise HTTPException(status_code=401, detail="Invalid credentials")

@app.get("/api/v1/auth/me")
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    return current_user

# 汉字相关
@app.get("/api/v1/characters/search")
async def search_characters(q: str = "", type: str = "all", sort: str = "relevance", limit: int = 20):
    results = []
    
    # 简单的搜索逻辑
    for char in MOCK_CHARACTERS:
        if (q.lower() in char["character"] or 
            q.lower() in char["pinyin"].lower() or 
            q.lower() in char["meaning_en"].lower() or 
            q.lower() in char["meaning_zh"]):
            results.append(char)
    
    return {
        "characters": results[:limit],
        "total": len(results)
    }

@app.get("/api/v1/characters/popular")
async def get_popular_characters(type: str = "characters", limit: int = 10):
    if type == "characters":
        return {"characters": MOCK_CHARACTERS[:limit]}
    else:
        return {"idioms": MOCK_IDIOMS[:limit]}

# AI推荐
@app.post("/api/v1/characters/recommend")
async def get_recommendations(request: RecommendationRequest, current_user: dict = Depends(get_current_user)):
    # 模拟AI推荐
    recommendations = {
        "characters": MOCK_CHARACTERS[:3],
        "idioms": MOCK_IDIOMS[:2],
        "ai_explanation": f"基于您的关键词 {', '.join(request.keywords)}，我为您推荐了这些具有深刻文化内涵的汉字和成语。"
    }
    
    return recommendations

# 纹身设计
@app.get("/api/v1/tattoos/styles")
async def get_tattoo_styles():
    return MOCK_STYLES

@app.post("/api/v1/tattoos/generate")
async def generate_tattoo(request: TattooRequest, current_user: dict = Depends(get_current_user)):
    # 模拟纹身生成
    result = {
        "id": 1,
        "content": request.content,
        "style": request.style,
        "position": request.position,
        "image_url": f"https://via.placeholder.com/512x512/1a1a1a/ffd700?text={request.content}",
        "thumbnail_url": f"https://via.placeholder.com/256x256/1a1a1a/ffd700?text={request.content}",
        "width": 1024 if request.high_resolution else 512,
        "height": 1024 if request.high_resolution else 512,
        "is_high_resolution": request.high_resolution,
        "is_watermarked": not request.high_resolution,
        "generation_time": 3.5,
        "quality_score": 0.92,
        "created_at": datetime.now().isoformat()
    }
    
    return result

@app.get("/api/v1/users/tattoo-history")
async def get_user_tattoo_history(current_user: dict = Depends(get_current_user)):
    # 返回空的历史记录
    return []

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
