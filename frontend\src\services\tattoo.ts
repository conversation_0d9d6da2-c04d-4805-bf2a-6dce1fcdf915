// 纹身服务
import api from './api'
import type {
  TattooStyle,
  TattooImage,
  TattooRequest
} from '@/types'

export class TattooService {
  // 获取纹身风格列表
  static async getTattooStyles(): Promise<TattooStyle[]> {
    const response = await api.get<TattooStyle[]>('/tattoos/styles')
    return response.data
  }

  // 生成纹身图片
  static async generateTattooImage(params: {
    content: string
    style: string
    position: string
    high_resolution?: boolean
  }): Promise<TattooImage> {
    // 首先创建纹身请求
    const requestData = {
      keywords: [params.content, params.style, params.position], // 包含内容、风格、位置作为关键词
      description: `为${params.content}生成${params.style}风格的纹身设计，位置：${params.position}`,
      selected_type: params.content.length === 1 ? 'character' : 'idiom',
      selected_content: params.content,
      preferred_style: params.style,
      preferred_position: params.position
    }

    const requestResponse = await api.post<TattooRequest>('/tattoos/requests', requestData)

    // 然后生成图片
    const generateData = {
      request_id: requestResponse.data.id,
      style: params.style,
      position: params.position,
      content: params.content,
      high_resolution: params.high_resolution || false
    }

    const response = await api.post<TattooImage>('/tattoos/generate', generateData, {
      timeout: 600000 // 10分钟超时，专门为图像生成设置
    })
    return response.data
  }

  // 解锁高分辨率图片
  static async unlockHighResolution(imageId: number): Promise<TattooImage> {
    const response = await api.post(`/tattoos/images/${imageId}/unlock`)
    return response.data
  }

  // 获取用户纹身历史
  static async getUserTattooHistory(params?: {
    page?: number
    limit?: number
  }): Promise<TattooImage[]> {
    const response = await api.get<TattooImage[]>('/users/tattoo-history', {
      params
    })
    return response.data
  }

  // 获取纹身请求详情
  static async getTattooRequestDetail(requestId: number): Promise<TattooRequest> {
    const response = await api.get<TattooRequest>(`/tattoos/requests/${requestId}`)
    return response.data
  }

  // 获取纹身图片详情
  static async getTattooImageDetail(imageId: number): Promise<TattooImage> {
    const response = await api.get<TattooImage>(`/tattoos/images/${imageId}`)
    return response.data
  }
}
