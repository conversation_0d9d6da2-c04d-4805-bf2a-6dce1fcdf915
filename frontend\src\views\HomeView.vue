<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Search, MagicStick, Picture, Star } from '@element-plus/icons-vue'

const router = useRouter()

// 示例数据
const featuredCharacters = ref([
  { char: '爱', pinyin: 'ài', meaning: 'Love, Affection', popularity: 95 },
  { char: '智', pinyin: 'zhì', meaning: 'Wisdom, Intelligence', popularity: 88 },
  { char: '勇', pinyin: 'yǒng', meaning: 'Courage, Bravery', popularity: 92 },
  { char: '和', pinyin: 'hé', meaning: 'Harmony, Peace', popularity: 85 }
])

const features = ref([
  {
    icon: Search,
    title: '智能搜索',
    description: '搜索数千个汉字和成语，了解其深层文化含义'
  },
  {
    icon: MagicStick,
    title: 'AI推荐',
    description: '基于您的需求，AI为您推荐最合适的汉字组合'
  },
  {
    icon: Picture,
    title: '纹身设计',
    description: '生成个性化的纹身预览图，多种风格可选'
  }
])

const handleGetStarted = () => {
  router.push('/search')
}

const handleCharacterClick = (char: string) => {
  router.push(`/search?q=${char}`)
}
</script>

<template>
  <div class="home-page">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">
              <span class="chinese-title">墨痕智纹</span>
              <span class="english-title">Chinese Character Tattoo AI</span>
            </h1>
            <p class="hero-description">
              探索汉字的深层文化内涵，让AI为您推荐最适合的纹身设计。
              每一个汉字都承载着千年的文化智慧，让您的纹身不仅美观，更有意义。
            </p>
            <div class="hero-actions">
              <el-button
                type="primary"
                size="large"
                @click="handleGetStarted"
                class="cta-button"
              >
                开始探索
                <el-icon class="ml-2"><Search /></el-icon>
              </el-button>
              <el-button
                size="large"
                @click="$router.push('/about')"
                class="secondary-button"
              >
                了解更多
              </el-button>
            </div>
          </div>

          <div class="hero-visual">
            <div class="character-showcase">
              <div class="main-character">智</div>
              <div class="floating-chars">
                <span class="float-char char-1">爱</span>
                <span class="float-char char-2">和</span>
                <span class="float-char char-3">勇</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 特色功能 -->
    <section class="features-section">
      <div class="container">
        <div class="section-header">
          <h2>为什么选择墨痕智纹？</h2>
          <p>结合传统文化与现代AI技术，为您提供专业的汉字纹身建议</p>
        </div>

        <div class="features-grid">
          <div
            v-for="(feature, index) in features"
            :key="index"
            class="feature-card"
          >
            <div class="feature-icon">
              <el-icon :size="32">
                <component :is="feature.icon" />
              </el-icon>
            </div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 热门汉字 -->
    <section class="popular-section">
      <div class="container">
        <div class="section-header">
          <h2>热门汉字推荐</h2>
          <p>最受欢迎的纹身汉字，点击了解更多</p>
        </div>

        <div class="characters-grid">
          <div
            v-for="char in featuredCharacters"
            :key="char.char"
            class="character-card"
            @click="handleCharacterClick(char.char)"
          >
            <div class="character-main">{{ char.char }}</div>
            <div class="character-info">
              <div class="pinyin">{{ char.pinyin }}</div>
              <div class="meaning">{{ char.meaning }}</div>
              <div class="popularity">
                <el-icon><Star /></el-icon>
                <span>{{ char.popularity }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2>准备开始您的纹身之旅了吗？</h2>
          <p>让AI为您推荐最适合的汉字，创造独一无二的纹身设计</p>
          <el-button
            type="primary"
            size="large"
            @click="$router.push('/recommend')"
            class="cta-button"
          >
            获取AI推荐
            <el-icon class="ml-2"><MagicStick /></el-icon>
          </el-button>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
}

/* 英雄区域 */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-title {
  margin: 0 0 24px 0;
}

.chinese-title {
  display: block;
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffd700;
  font-family: 'SimSun', serif;
  margin-bottom: 8px;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.english-title {
  display: block;
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 2px;
}

.hero-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 32px;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.cta-button {
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 25px;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.secondary-button {
  padding: 12px 32px;
  font-size: 16px;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  transition: all 0.3s ease;
}

.secondary-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 视觉展示区域 */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.character-showcase {
  position: relative;
  width: 300px;
  height: 300px;
}

.main-character {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 8rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
  animation: pulse 3s ease-in-out infinite;
}

.floating-chars {
  position: absolute;
  width: 100%;
  height: 100%;
}

.float-char {
  position: absolute;
  font-size: 2rem;
  color: rgba(255, 215, 0, 0.6);
  font-family: 'SimSun', serif;
  animation: float 4s ease-in-out infinite;
}

.char-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.char-2 {
  top: 70%;
  right: 15%;
  animation-delay: 1s;
}

.char-3 {
  top: 30%;
  right: 20%;
  animation-delay: 2s;
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.05); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

/* 通用容器和区域样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.section-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
}

/* 特色功能区域 */
.features-section {
  padding: 100px 0;
  background: rgba(255, 255, 255, 0.02);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.feature-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: #1a1a1a;
}

.feature-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
}

/* 热门汉字区域 */
.popular-section {
  padding: 100px 0;
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.character-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 30px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.character-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 215, 0, 0.3);
}

.character-main {
  font-size: 4rem;
  font-weight: bold;
  color: #ffd700;
  font-family: 'SimSun', serif;
  margin-bottom: 16px;
  text-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.character-info .pinyin {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  font-style: italic;
}

.character-info .meaning {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 12px;
}

.popularity {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: #ffd700;
  font-weight: 600;
}

/* CTA区域 */
.cta-section {
  padding: 100px 0;
  background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
}

.cta-content {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.cta-content h2 {
  font-size: 2.5rem;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 16px 0;
}

.cta-content p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 32px;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .chinese-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 80vh;
  }

  .chinese-title {
    font-size: 2.5rem;
  }

  .english-title {
    font-size: 1.2rem;
  }

  .hero-actions {
    justify-content: center;
  }

  .character-showcase {
    width: 250px;
    height: 250px;
  }

  .main-character {
    font-size: 6rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .features-grid,
  .characters-grid {
    grid-template-columns: 1fr;
  }
}
</style>
