#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试纹身API
"""

import requests
import json

def test_tattoo_api():
    """测试纹身API"""
    
    base_url = "http://localhost:8000/api/v1"
    
    # 首先登录获取token
    login_data = {
        "username": "yangshu888",
        "password": "123456"
    }
    
    print("🔐 登录...")
    login_response = requests.post(f"{base_url}/auth/login", data=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(login_response.text)
        return
    
    token = login_response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    print("✅ 登录成功")
    
    # 测试创建纹身请求
    tattoo_request_data = {
        "keywords": ["智", "书法", "前臂"],
        "description": "为智生成书法风格的纹身设计，位置：前臂",
        "selected_type": "character",
        "selected_content": "智",
        "preferred_style": "书法",
        "preferred_position": "前臂"
    }
    
    print("🎨 创建纹身请求...")
    request_response = requests.post(
        f"{base_url}/tattoos/requests",
        headers=headers,
        json=tattoo_request_data
    )
    
    if request_response.status_code != 200:
        print(f"❌ 创建纹身请求失败: {request_response.status_code}")
        print(request_response.text)
        return
    
    request_data = request_response.json()
    print("✅ 纹身请求创建成功")
    print(f"   请求ID: {request_data['id']}")
    
    # 测试生成纹身图片
    generation_data = {
        "request_id": request_data["id"],
        "style": "书法",
        "position": "前臂",
        "content": "智",
        "high_resolution": False
    }
    
    print("🖼️ 生成纹身图片...")
    generation_response = requests.post(
        f"{base_url}/tattoos/generate",
        headers=headers,
        json=generation_data
    )
    
    if generation_response.status_code != 200:
        print(f"❌ 生成纹身图片失败: {generation_response.status_code}")
        print(generation_response.text)
        return
    
    image_data = generation_response.json()
    print("✅ 纹身图片生成成功")
    print(f"   图片URL: {image_data['image_url']}")
    print(f"   模型: {image_data.get('generation_model', 'unknown')}")

if __name__ == "__main__":
    test_tattoo_api()
