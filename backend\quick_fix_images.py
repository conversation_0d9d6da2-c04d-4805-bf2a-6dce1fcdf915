#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复tattoo_images表
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db.database import engine

def quick_fix():
    """快速修复"""
    
    print("🔧 快速修复tattoo_images表...")
    
    with engine.connect() as conn:
        try:
            print("添加 file_size 字段...")
            conn.execute(text("ALTER TABLE tattoo_images ADD COLUMN file_size INT"))
            conn.commit()
            print("✅ file_size 字段添加成功")
        except Exception as e:
            print(f"file_size 字段可能已存在: {e}")
        
        try:
            print("添加 generation_prompt 字段...")
            conn.execute(text("ALTER TABLE tattoo_images ADD COLUMN generation_prompt TEXT"))
            conn.commit()
            print("✅ generation_prompt 字段添加成功")
        except Exception as e:
            print(f"generation_prompt 字段可能已存在: {e}")
        
        try:
            print("添加 generation_model 字段...")
            conn.execute(text("ALTER TABLE tattoo_images ADD COLUMN generation_model VARCHAR(50)"))
            conn.commit()
            print("✅ generation_model 字段添加成功")
        except Exception as e:
            print(f"generation_model 字段可能已存在: {e}")
        
        # 检查最终表结构
        print("📋 最终表结构:")
        result = conn.execute(text("DESCRIBE tattoo_images"))
        for row in result.fetchall():
            print(f"  {row[0]}: {row[1]}")

if __name__ == "__main__":
    quick_fix()
