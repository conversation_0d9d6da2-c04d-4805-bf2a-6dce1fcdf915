#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复编码问题的数据库初始化脚本
"""

import os
import sys
import locale
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError

# 设置环境编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PGCLIENTENCODING'] = 'UTF8'

def set_encoding():
    """设置正确的编码"""
    try:
        # 设置Python的默认编码
        if sys.platform.startswith('win'):
            # Windows系统设置
            locale.setlocale(locale.LC_ALL, 'Chinese (Simplified)_China.utf8')
        else:
            # Unix系统设置
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
    except locale.Error:
        print("Warning: Could not set locale, using default")

def create_database():
    """创建数据库"""
    try:
        print("正在连接PostgreSQL...")
        
        # 使用更简单的连接字符串
        engine = create_engine(
            "postgresql://postgres:password123@localhost:5432/postgres",
            isolation_level="AUTOCOMMIT",
            connect_args={
                "client_encoding": "utf8"
            }
        )
        
        with engine.connect() as conn:
            # 检查数据库是否存在
            result = conn.execute(text(
                "SELECT 1 FROM pg_database WHERE datname = 'chinese_tattoo_ai'"
            ))
            
            if not result.fetchone():
                # 创建数据库，明确指定编码
                conn.execute(text(
                    "CREATE DATABASE chinese_tattoo_ai "
                    "WITH ENCODING 'UTF8' "
                    "LC_COLLATE='C' "
                    "LC_CTYPE='C' "
                    "TEMPLATE=template0"
                ))
                print("✓ 数据库 'chinese_tattoo_ai' 创建成功")
            else:
                print("✓ 数据库 'chinese_tattoo_ai' 已存在")
                
    except OperationalError as e:
        error_msg = str(e)
        print(f"❌ 数据库连接失败: {error_msg}")
        
        if "password authentication failed" in error_msg:
            print("解决方案：请检查PostgreSQL密码是否为 'password123'")
        elif "could not connect to server" in error_msg:
            print("解决方案：请确保PostgreSQL服务已启动")
        elif "database" in error_msg and "does not exist" in error_msg:
            print("解决方案：请确保PostgreSQL已正确安装")
        
        return False
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False
    
    return True

def create_tables():
    """创建数据表"""
    try:
        print("正在创建数据表...")
        
        # 连接到目标数据库
        engine = create_engine(
            "postgresql://postgres:password123@localhost:5432/chinese_tattoo_ai",
            connect_args={
                "client_encoding": "utf8"
            }
        )
        
        with engine.connect() as conn:
            # 创建用户表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS users (
                    id SERIAL PRIMARY KEY,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    username VARCHAR(100) UNIQUE NOT NULL,
                    hashed_password VARCHAR(255) NOT NULL,
                    full_name VARCHAR(255),
                    is_active BOOLEAN DEFAULT TRUE,
                    is_verified BOOLEAN DEFAULT FALSE,
                    is_premium BOOLEAN DEFAULT FALSE,
                    credits INTEGER DEFAULT 10,
                    avatar_url VARCHAR(500),
                    bio TEXT,
                    language_preference VARCHAR(10) DEFAULT 'zh',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            """))
            
            # 创建汉字表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS chinese_characters (
                    id SERIAL PRIMARY KEY,
                    character VARCHAR(10) UNIQUE NOT NULL,
                    pinyin VARCHAR(50) NOT NULL,
                    meaning_en TEXT NOT NULL,
                    meaning_zh TEXT NOT NULL,
                    cultural_context TEXT,
                    category VARCHAR(50),
                    tags TEXT[],
                    tattoo_popularity INTEGER DEFAULT 0,
                    recommended_positions TEXT[],
                    is_active BOOLEAN DEFAULT TRUE,
                    is_verified BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # 创建成语表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS chinese_idioms (
                    id SERIAL PRIMARY KEY,
                    idiom VARCHAR(50) UNIQUE NOT NULL,
                    pinyin VARCHAR(200) NOT NULL,
                    meaning_en TEXT NOT NULL,
                    meaning_zh TEXT NOT NULL,
                    origin_story TEXT,
                    cultural_context TEXT,
                    category VARCHAR(50),
                    tags TEXT[],
                    character_count INTEGER NOT NULL,
                    difficulty_level INTEGER DEFAULT 1,
                    tattoo_popularity INTEGER DEFAULT 0,
                    recommended_positions TEXT[],
                    is_active BOOLEAN DEFAULT TRUE,
                    is_verified BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # 创建纹身请求表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS tattoo_requests (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER REFERENCES users(id),
                    keywords TEXT[],
                    description TEXT,
                    personal_meaning TEXT,
                    recommended_characters JSONB,
                    recommended_idioms JSONB,
                    ai_explanation TEXT,
                    selected_type VARCHAR(20),
                    selected_content VARCHAR(100),
                    preferred_style VARCHAR(50),
                    preferred_position VARCHAR(50),
                    size_preference VARCHAR(20),
                    status VARCHAR(20) DEFAULT 'pending',
                    is_paid BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # 创建纹身图片表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS tattoo_images (
                    id SERIAL PRIMARY KEY,
                    request_id INTEGER REFERENCES tattoo_requests(id),
                    image_url VARCHAR(500) NOT NULL,
                    thumbnail_url VARCHAR(500),
                    watermarked_url VARCHAR(500),
                    style VARCHAR(50) NOT NULL,
                    position VARCHAR(50) NOT NULL,
                    content VARCHAR(100) NOT NULL,
                    width INTEGER,
                    height INTEGER,
                    file_size INTEGER,
                    generation_prompt TEXT,
                    generation_model VARCHAR(50),
                    generation_time FLOAT,
                    is_high_resolution BOOLEAN DEFAULT FALSE,
                    is_watermarked BOOLEAN DEFAULT TRUE,
                    quality_score FLOAT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            # 创建纹身风格表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS tattoo_styles (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(50) UNIQUE NOT NULL,
                    name_zh VARCHAR(50) NOT NULL,
                    description TEXT,
                    characteristics JSONB,
                    suitable_positions TEXT[],
                    difficulty_level INTEGER DEFAULT 1,
                    example_images TEXT[],
                    prompt_template TEXT,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_premium BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))
            
            conn.commit()
            print("✓ 数据表创建成功")
            
    except Exception as e:
        print(f"❌ 创建数据表失败: {e}")
        return False
    
    return True

def insert_sample_data():
    """插入示例数据（使用ASCII字符避免编码问题）"""
    try:
        print("正在插入示例数据...")
        
        engine = create_engine(
            "postgresql://postgres:password123@localhost:5432/chinese_tattoo_ai",
            connect_args={
                "client_encoding": "utf8"
            }
        )
        
        with engine.connect() as conn:
            # 插入纹身风格（先插入英文数据）
            conn.execute(text("""
                INSERT INTO tattoo_styles (name, name_zh, description, difficulty_level)
                VALUES 
                ('calligraphy', 'Traditional Calligraphy', 'Traditional Chinese calligraphy style', 3),
                ('modern', 'Modern Minimalist', 'Clean and modern design style', 2),
                ('watercolor', 'Watercolor Style', 'Soft watercolor painting effect', 4),
                ('tribal', 'Tribal Style', 'Bold tribal pattern design', 3)
                ON CONFLICT (name) DO NOTHING
            """))
            
            # 插入基础汉字数据（使用Unicode转义）
            conn.execute(text("""
                INSERT INTO chinese_characters (character, pinyin, meaning_en, meaning_zh, cultural_context, tattoo_popularity)
                VALUES 
                ('爱', 'ai', 'love, affection', 'love and care', 'Symbol of love and compassion', 95),
                ('智', 'zhi', 'wisdom, intelligence', 'wisdom and knowledge', 'Symbol of wisdom and intelligence', 88),
                ('勇', 'yong', 'courage, bravery', 'courage and bravery', 'Symbol of courage and strength', 92),
                ('和', 'he', 'harmony, peace', 'harmony and peace', 'Symbol of harmony and balance', 85),
                ('福', 'fu', 'fortune, blessing', 'fortune and happiness', 'Symbol of good fortune', 98)
                ON CONFLICT (character) DO NOTHING
            """))
            
            conn.commit()
            print("✓ 示例数据插入成功")
            
    except Exception as e:
        print(f"❌ 插入示例数据失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=== 汉字纹身AI平台数据库初始化（修复版）===")
    
    # 设置编码
    set_encoding()
    
    # 创建数据库
    if not create_database():
        print("\n请检查以下事项：")
        print("1. PostgreSQL服务是否已启动")
        print("2. 用户名是否为 'postgres'")
        print("3. 密码是否为 'password123'")
        print("4. 端口5432是否可访问")
        sys.exit(1)
    
    # 创建数据表
    if not create_tables():
        sys.exit(1)
    
    # 插入示例数据
    if not insert_sample_data():
        sys.exit(1)
    
    print("\n🎉 数据库初始化完成！")
    print("现在可以启动后端服务了：")
    print("uvicorn main:app --reload --host 0.0.0.0 --port 8000")

if __name__ == "__main__":
    main()
