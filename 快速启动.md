# 🚀 汉字纹身AI平台 - 快速启动指南

## 📋 前置要求

确保您的系统已安装：
- Python 3.11+
- Node.js 18+
- PostgreSQL 15+

## 🗄️ 数据库设置

1. 启动PostgreSQL服务
2. 创建数据库：

```sql
-- 连接PostgreSQL
psql -U postgres

-- 创建数据库
CREATE DATABASE chinese_tattoo_ai;

-- 退出
\q
```

## 🔧 后端启动

```bash
# 1. 进入后端目录
cd backend

# 2. 创建并激活虚拟环境
python -m venv venv
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 3. 安装依赖
pip install -r requirements.txt

# 4. 复制环境配置
copy .env.example .env
# 或 cp .env.example .env

# 5. 初始化数据库
python init_db.py

# 6. 启动后端服务
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 🎨 前端启动

**新开一个终端窗口：**

```bash
# 1. 进入前端目录
cd frontend

# 2. 安装依赖
npm install

# 3. 启动前端服务
npm run dev
```

## 🌐 访问应用

- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 🧪 快速测试

1. **注册账户**：
   - 访问 http://localhost:5173
   - 点击"注册"
   - 填写信息并注册

2. **测试搜索**：
   - 登录后点击"搜索"
   - 输入"爱"或"love"测试

3. **测试AI推荐**：
   - 点击"AI推荐"
   - 输入关键词：love, strength
   - 查看推荐结果

4. **测试纹身设计**：
   - 点击"纹身设计"
   - 输入汉字"智"
   - 生成设计预览

## ⚠️ 常见问题

### 数据库连接失败
- 确保PostgreSQL服务已启动
- 检查数据库名称是否正确
- 验证用户名密码

### 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :8000
netstat -ano | findstr :5173

# 杀死占用进程
taskkill /PID <进程ID> /F
```

### Python依赖安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 🎉 成功标志

看到以下内容说明启动成功：
- 前端页面正常显示
- 可以注册和登录用户
- 搜索功能返回结果
- 各个页面可以正常访问

## 📞 需要帮助？

如果遇到问题，请：
1. 检查终端错误信息
2. 查看浏览器控制台
3. 确认所有服务都在运行
4. 参考 `docs/测试指南.md` 获取详细信息

祝您使用愉快！🎊
