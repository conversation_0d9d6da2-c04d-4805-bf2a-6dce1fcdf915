version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: chinese_tattoo_postgres
    environment:
      POSTGRES_DB: chinese_tattoo_ai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    networks:
      - tattoo_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: chinese_tattoo_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - tattoo_network

  # 后端API服务
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/Dockerfile.backend
    container_name: chinese_tattoo_backend
    environment:
      - DATABASE_URL=***********************************************/chinese_tattoo_ai
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=true
    ports:
      - "8000:8000"
    volumes:
      - ../backend:/app
      - backend_uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - tattoo_network
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # 前端服务
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/Dockerfile.frontend
    container_name: chinese_tattoo_frontend
    ports:
      - "3000:3000"
    volumes:
      - ../frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
    depends_on:
      - backend
    networks:
      - tattoo_network
    command: npm run dev -- --host 0.0.0.0 --port 3000

volumes:
  postgres_data:
  redis_data:
  backend_uploads:

networks:
  tattoo_network:
    driver: bridge
