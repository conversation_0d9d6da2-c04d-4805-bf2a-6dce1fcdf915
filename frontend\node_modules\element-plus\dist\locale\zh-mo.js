/*! Element Plus v2.9.11 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleZhMo = factory());
})(this, (function () { 'use strict';

  var zhMo = {
    name: "zh-mo",
    el: {
      breadcrumb: {
        label: "\u9EB5\u5305\u5C51"
      },
      colorpicker: {
        confirm: "\u78BA\u8A8D",
        clear: "\u6E05\u7A7A",
        defaultLabel: "\u984F\u8272\u9078\u64C7\u5668",
        description: "\u7576\u524D\u984F\u8272\u70BA {color}\u3002\u6309 Enter \u9375\u9078\u64C7\u65B0\u984F\u8272\u3002",
        alphaLabel: "\u9078\u64C7\u900F\u660E\u5EA6\u7684\u503C"
      },
      datepicker: {
        now: "\u73FE\u5728",
        today: "\u4ECA\u5929",
        cancel: "\u53D6\u6D88",
        clear: "\u6E05\u7A7A",
        confirm: "\u78BA\u8A8D",
        dateTablePrompt: "\u4F7F\u7528\u65B9\u5411\u9375\u8207 Enter \u9375\u4EE5\u9078\u64C7\u65E5\u671F",
        monthTablePrompt: "\u4F7F\u7528\u65B9\u5411\u9375\u8207 Enter \u9375\u4EE5\u9078\u64C7\u6708\u4EFD",
        yearTablePrompt: "\u4F7F\u7528\u65B9\u5411\u9375\u8207 Enter \u9375\u4EE5\u9078\u64C7\u5E74\u4EFD",
        selectedDate: "\u5DF2\u9078\u65E5\u671F",
        selectDate: "\u9078\u64C7\u65E5\u671F",
        selectTime: "\u9078\u64C7\u6642\u9593",
        startDate: "\u958B\u59CB\u65E5\u671F",
        startTime: "\u958B\u59CB\u6642\u9593",
        endDate: "\u7D50\u675F\u65E5\u671F",
        endTime: "\u7D50\u675F\u6642\u9593",
        prevYear: "\u524D\u4E00\u5E74",
        nextYear: "\u5F8C\u4E00\u5E74",
        prevMonth: "\u4E0A\u500B\u6708",
        nextMonth: "\u4E0B\u500B\u6708",
        year: "\u5E74",
        month1: "1 \u6708",
        month2: "2 \u6708",
        month3: "3 \u6708",
        month4: "4 \u6708",
        month5: "5 \u6708",
        month6: "6 \u6708",
        month7: "7 \u6708",
        month8: "8 \u6708",
        month9: "9 \u6708",
        month10: "10 \u6708",
        month11: "11 \u6708",
        month12: "12 \u6708",
        weeks: {
          sun: "\u65E5",
          mon: "\u4E00",
          tue: "\u4E8C",
          wed: "\u4E09",
          thu: "\u56DB",
          fri: "\u4E94",
          sat: "\u516D"
        },
        weeksFull: {
          sun: "\u661F\u671F\u65E5",
          mon: "\u661F\u671F\u4E00",
          tue: "\u661F\u671F\u4E8C",
          wed: "\u661F\u671F\u4E09",
          thu: "\u661F\u671F\u56DB",
          fri: "\u661F\u671F\u4E94",
          sat: "\u661F\u671F\u516D"
        },
        months: {
          jan: "\u4E00\u6708",
          feb: "\u4E8C\u6708",
          mar: "\u4E09\u6708",
          apr: "\u56DB\u6708",
          may: "\u4E94\u6708",
          jun: "\u516D\u6708",
          jul: "\u4E03\u6708",
          aug: "\u516B\u6708",
          sep: "\u4E5D\u6708",
          oct: "\u5341\u6708",
          nov: "\u5341\u4E00\u6708",
          dec: "\u5341\u4E8C\u6708"
        }
      },
      inputNumber: {
        decrease: "\u6E1B\u5C11\u6578\u503C",
        increase: "\u589E\u52A0\u6578\u503C"
      },
      select: {
        loading: "\u8F09\u5165\u4E2D",
        noMatch: "\u7121\u5339\u914D\u8CC7\u6599",
        noData: "\u7121\u8CC7\u6599",
        placeholder: "\u8ACB\u9078\u64C7"
      },
      mention: {
        loading: "\u8F09\u5165\u4E2D"
      },
      dropdown: {
        toggleDropdown: "\u5207\u63DB\u4E0B\u62C9\u9078\u55AE"
      },
      cascader: {
        noMatch: "\u7121\u5339\u914D\u8CC7\u6599",
        loading: "\u8F09\u5165\u4E2D",
        placeholder: "\u8ACB\u9078\u64C7",
        noData: "\u7121\u8CC7\u6599"
      },
      pagination: {
        goto: "\u524D\u5F80",
        pagesize: "\u9805/\u9801",
        total: "\u5171 {total} \u9805",
        pageClassifier: "\u9801",
        page: "\u9801",
        prev: "\u4E0A\u4E00\u9801",
        next: "\u4E0B\u4E00\u9801",
        currentPage: "\u7B2C {pager} \u9801",
        prevPages: "\u5411\u524D {pager} \u9801",
        nextPages: "\u5411\u5F8C {pager} \u9801",
        deprecationWarning: "\u6AA2\u6E2C\u5230\u5DF2\u904E\u6642\u7684\u4F7F\u7528\u65B9\u5F0F\uFF0C\u8ACB\u53C3\u95B1 el-pagination \u8AAA\u660E\u6587\u4EF6\u4EE5\u4E86\u89E3\u66F4\u591A\u8CC7\u8A0A"
      },
      dialog: {
        close: "\u95DC\u9589\u6B64\u5C0D\u8A71\u6846"
      },
      drawer: {
        close: "\u95DC\u9589\u6B64\u5C0D\u8A71\u6846"
      },
      messagebox: {
        title: "\u63D0\u793A",
        confirm: "\u78BA\u5B9A",
        cancel: "\u53D6\u6D88",
        error: "\u8F38\u5165\u7684\u8CC7\u6599\u4E0D\u7B26\u5408\u898F\u5B9A!",
        close: "\u95DC\u9589\u6B64\u5C0D\u8A71\u6846"
      },
      upload: {
        deleteTip: "\u6309 Delete \u9375\u4EE5\u522A\u9664",
        delete: "\u522A\u9664",
        preview: "\u67E5\u770B\u5716\u7247",
        continue: "\u7E7C\u7E8C\u4E0A\u50B3"
      },
      slider: {
        defaultLabel: "\u6ED1\u687F\u4ECB\u65BC {min} \u81F3 {max}",
        defaultRangeStartLabel: "\u9078\u64C7\u8D77\u59CB\u503C",
        defaultRangeEndLabel: "\u9078\u64C7\u7D50\u675F\u503C"
      },
      table: {
        emptyText: "\u66AB\u7121\u8CC7\u6599",
        confirmFilter: "\u7BE9\u9078",
        resetFilter: "\u91CD\u7F6E",
        clearFilter: "\u5168\u90E8",
        sumText: "\u5408\u8A08"
      },
      tour: {
        next: "\u4E0B\u4E00\u6B65",
        previous: "\u4E0A\u4E00\u6B65",
        finish: "\u7D50\u675F\u5C0E\u89BD"
      },
      tree: {
        emptyText: "\u66AB\u7121\u8CC7\u6599"
      },
      transfer: {
        noMatch: "\u7121\u5339\u914D\u8CC7\u6599",
        noData: "\u7121\u8CC7\u6599",
        titles: ["\u5217\u8868 1", "\u5217\u8868 2"],
        filterPlaceholder: "\u8ACB\u8F38\u5165\u641C\u5C0B\u5167\u5BB9",
        noCheckedFormat: "\u5171 {total} \u9805",
        hasCheckedFormat: "\u5DF2\u9078 {checked}/{total} \u9805"
      },
      image: {
        error: "\u8F09\u5165\u5931\u6557"
      },
      pageHeader: {
        title: "\u8FD4\u56DE"
      },
      popconfirm: {
        confirmButtonText: "\u78BA\u8A8D",
        cancelButtonText: "\u53D6\u6D88"
      },
      carousel: {
        leftArrow: "\u4E0A\u4E00\u5F35\u5E7B\u71C8\u7247",
        rightArrow: "\u4E0B\u4E00\u5F35\u5E7B\u71C8\u7247",
        indicator: "\u5E7B\u71C8\u7247\u5207\u63DB\u81F3\u7D22\u5F15 {index}"
      }
    }
  };

  return zhMo;

}));
