#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI API调用
"""

import asyncio
import httpx
import json

async def test_ai_api():
    """直接测试AI API调用"""

    print("🔍 开始测试AI API...")

    # API配置
    api_key = "sk-fbetqnslawacbyvkjqiwkswqjqqxqbngkggsyuktozmkxitj"
    base_url = "https://api.siliconflow.cn/v1"
    model = "deepseek-ai/DeepSeek-R1"  # 思维型模型

    # 构建请求
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    prompt = """请推荐2个汉字和2个成语，用于纹身设计。

要求：代表勇气和智慧

请用JSON格式返回：
{
  "ai_explanation": "简短说明",
  "characters": [
    {"character": "智", "pinyin": "zhì", "meaning_en": "wisdom", "meaning_zh": "智慧", "explanation": "简短解释", "cultural_context": "文化背景", "score": 9.0}
  ],
  "idioms": [
    {"idiom": "自强不息", "pinyin": "zì qiáng bù xī", "meaning_en": "constantly strive", "meaning_zh": "永不懈怠", "explanation": "简短解释", "origin_story": "来源", "character_count": 4, "score": 9.0}
  ]
}

必须返回2个汉字和2个成语。"""

    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.7,
        "max_tokens": 2000
    }

    try:
        print(f"🔑 API密钥: {api_key[:20]}...")
        print(f"🌐 API地址: {base_url}")
        print(f"🤖 模型: {model}")
        print("🚀 发送请求...")

        async with httpx.AsyncClient(timeout=180.0) as client:  # 思维型模型需要更长时间
            response = await client.post(
                f"{base_url}/chat/completions",
                headers=headers,
                json=data
            )

            print(f"� 响应状态: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]

                print("✅ API调用成功！")
                print("📋 AI返回内容:")
                print(content)

                # 尝试解析JSON
                try:
                    # 清理markdown格式
                    clean_content = content.strip()
                    if clean_content.startswith("```json"):
                        clean_content = clean_content[7:]
                    if clean_content.endswith("```"):
                        clean_content = clean_content[:-3]
                    clean_content = clean_content.strip()

                    recommendations = json.loads(clean_content)
                    print("\n🎯 解析结果:")
                    print(f"   汉字数量: {len(recommendations.get('characters', []))}")
                    print(f"   成语数量: {len(recommendations.get('idioms', []))}")

                    # 显示推荐内容
                    print("\n🔤 汉字推荐:")
                    for i, char in enumerate(recommendations.get('characters', []), 1):
                        print(f"   {i}. {char.get('character', '')} ({char.get('pinyin', '')})")
                        print(f"      含义: {char.get('meaning_zh', '')}")

                    print("\n📚 成语推荐:")
                    for i, idiom in enumerate(recommendations.get('idioms', []), 1):
                        print(f"   {i}. {idiom.get('idiom', '')} ({idiom.get('pinyin', '')})")
                        print(f"      含义: {idiom.get('meaning_zh', '')}")

                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"清理后的内容: {clean_content[:200]}...")

            else:
                print(f"❌ API调用失败: {response.status_code}")
                print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 请求异常: {e}")
        print(f"异常类型: {type(e).__name__}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ai_api())
