"""
纹身服务
Tattoo Service for managing tattoo requests and image generation
"""

import time
import uuid
from typing import Optional
from sqlalchemy.orm import Session
from app.models.tattoo import TattooRequest, TattooImage, TattooStyle
from app.schemas.tattoo import TattooRequestCreate, TattooImageCreate
from app.services.image_generation_service import ImageGenerationService

class TattooService:
    """纹身服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.image_service = ImageGenerationService()
    
    def create_request(
        self, 
        user_id: int, 
        request_data: TattooRequestCreate
    ) -> TattooRequest:
        """创建纹身请求"""
        
        db_request = TattooRequest(
            user_id=user_id,
            keywords=request_data.keywords,
            description=request_data.description,
            personal_meaning=request_data.personal_meaning,
            selected_type=request_data.selected_type,
            selected_content=request_data.selected_content,
            preferred_style=request_data.preferred_style,
            preferred_position=request_data.preferred_position,
            size_preference=request_data.size_preference,
            status="pending"
        )
        
        self.db.add(db_request)
        self.db.commit()
        self.db.refresh(db_request)
        
        return db_request
    
    def get_request_by_id(self, request_id: int) -> Optional[TattooRequest]:
        """通过ID获取纹身请求"""
        return self.db.query(TattooRequest).filter(
            TattooRequest.id == request_id
        ).first()
    
    def get_user_requests(
        self, 
        user_id: int, 
        skip: int = 0, 
        limit: int = 20
    ) -> list[TattooRequest]:
        """获取用户的纹身请求列表"""
        return self.db.query(TattooRequest).filter(
            TattooRequest.user_id == user_id
        ).order_by(
            TattooRequest.created_at.desc()
        ).offset(skip).limit(limit).all()
    
    async def generate_image(
        self,
        request_id: int,
        style: str,
        position: str,
        content: str,
        high_resolution: bool = False
    ) -> TattooImage:
        """生成纹身图片"""
        
        start_time = time.time()
        
        try:
            # 构建生成提示词
            prompt = self._build_generation_prompt(
                content, style, position, high_resolution
            )
            
            # 调用图像生成服务
            image_result = await self.image_service.generate_tattoo_image(
                prompt=prompt,
                high_resolution=high_resolution
            )
            
            generation_time = time.time() - start_time
            
            # 保存图片记录到数据库
            db_image = TattooImage(
                request_id=request_id,
                image_url=image_result["image_url"],
                thumbnail_url=image_result.get("thumbnail_url"),
                watermarked_url=image_result.get("watermarked_url"),
                style=style,
                position=position,
                content=content,
                width=image_result.get("width"),
                height=image_result.get("height"),
                file_size=image_result.get("file_size"),
                generation_prompt=prompt,
                generation_model=image_result.get("model", "stable-diffusion"),
                generation_time=generation_time,
                is_high_resolution=high_resolution,
                is_watermarked=not high_resolution,
                quality_score=image_result.get("quality_score")
            )
            
            self.db.add(db_image)
            self.db.commit()
            self.db.refresh(db_image)
            
            # 更新请求状态
            request = self.get_request_by_id(request_id)
            if request:
                request.status = "completed"
                self.db.commit()
            
            return db_image
            
        except Exception as e:
            # 更新请求状态为失败
            request = self.get_request_by_id(request_id)
            if request:
                request.status = "failed"
                self.db.commit()
            raise e
    
    def _build_generation_prompt(
        self, 
        content: str, 
        style: str, 
        position: str, 
        high_resolution: bool
    ) -> str:
        """构建图像生成提示词"""
        
        # 获取风格模板
        style_obj = self.db.query(TattooStyle).filter(
            TattooStyle.name == style,
            TattooStyle.is_active == True
        ).first()
        
        base_prompt = f"Chinese character tattoo design: '{content}'"
        
        if style_obj and style_obj.prompt_template:
            prompt = style_obj.prompt_template.format(
                content=content,
                position=position
            )
        else:
            # 默认提示词模板
            prompt = f"""
            {base_prompt}, {style} style, designed for {position}, 
            high quality tattoo design, black ink, clean lines, 
            traditional Chinese calligraphy, artistic, elegant, 
            suitable for tattoo, white background, centered composition
            """
        
        if high_resolution:
            prompt += ", ultra high resolution, 4K, detailed, professional quality"
        
        return prompt.strip()
    
    async def unlock_high_resolution(self, image_id: int) -> TattooImage:
        """解锁高分辨率图片"""
        
        image = self.db.query(TattooImage).filter(
            TattooImage.id == image_id
        ).first()
        
        if not image:
            raise ValueError("图片不存在")
        
        if image.is_high_resolution and not image.is_watermarked:
            return image
        
        try:
            # 重新生成高分辨率无水印版本
            prompt = image.generation_prompt
            if prompt:
                prompt += ", ultra high resolution, 4K, no watermark"
            
            image_result = await self.image_service.generate_tattoo_image(
                prompt=prompt or self._build_generation_prompt(
                    image.content, image.style, image.position, True
                ),
                high_resolution=True
            )
            
            # 更新图片记录
            image.image_url = image_result["image_url"]
            image.is_high_resolution = True
            image.is_watermarked = False
            
            if image_result.get("width"):
                image.width = image_result["width"]
            if image_result.get("height"):
                image.height = image_result["height"]
            if image_result.get("file_size"):
                image.file_size = image_result["file_size"]
            
            self.db.commit()
            self.db.refresh(image)
            
            return image
            
        except Exception as e:
            raise Exception(f"解锁高分辨率图片失败: {str(e)}")
    
    def get_image_by_id(self, image_id: int) -> Optional[TattooImage]:
        """通过ID获取纹身图片"""
        return self.db.query(TattooImage).filter(
            TattooImage.id == image_id
        ).first()
    
    def get_request_images(self, request_id: int) -> list[TattooImage]:
        """获取请求的所有图片"""
        return self.db.query(TattooImage).filter(
            TattooImage.request_id == request_id
        ).order_by(TattooImage.created_at.desc()).all()
    
    def get_available_styles(self) -> list[TattooStyle]:
        """获取可用的纹身风格"""
        return self.db.query(TattooStyle).filter(
            TattooStyle.is_active == True
        ).order_by(TattooStyle.name).all()
    
    def update_request_status(self, request_id: int, status: str) -> bool:
        """更新请求状态"""
        request = self.get_request_by_id(request_id)
        if request:
            request.status = status
            self.db.commit()
            return True
        return False
