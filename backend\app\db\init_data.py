"""
数据库初始化数据
Database Initialization Data
"""

from sqlalchemy.orm import Session
from app.models.character import ChineseCharacter, ChineseIdiom
from app.models.tattoo import TattooStyle

def init_sample_characters(db: Session):
    """初始化示例汉字数据"""
    
    sample_characters = [
        {
            "character": "爱",
            "pinyin": "ài",
            "meaning_en": "love, affection",
            "meaning_zh": "爱，喜爱，热爱",
            "cultural_context": "表达深厚的情感和关爱，是中华文化中最重要的情感之一。",
            "category": "emotion",
            "tags": ["love", "emotion", "family"],
            "tattoo_popularity": 95,
            "recommended_positions": ["wrist", "chest", "back"],
            "is_verified": True
        },
        {
            "character": "智",
            "pinyin": "zhì",
            "meaning_en": "wisdom, intelligence",
            "meaning_zh": "智慧，聪明，明智",
            "cultural_context": "代表智慧和聪明才智，在中华文化中被视为重要的品德。",
            "category": "virtue",
            "tags": ["wisdom", "intelligence", "virtue"],
            "tattoo_popularity": 88,
            "recommended_positions": ["forearm", "shoulder", "back"],
            "is_verified": True
        },
        {
            "character": "勇",
            "pinyin": "yǒng",
            "meaning_en": "courage, bravery",
            "meaning_zh": "勇敢，勇气，英勇",
            "cultural_context": "象征勇气和无畏精神，是中华民族推崇的品质。",
            "category": "virtue",
            "tags": ["courage", "bravery", "strength"],
            "tattoo_popularity": 92,
            "recommended_positions": ["chest", "arm", "shoulder"],
            "is_verified": True
        },
        {
            "character": "和",
            "pinyin": "hé",
            "meaning_en": "harmony, peace",
            "meaning_zh": "和谐，和平，和睦",
            "cultural_context": "体现中华文化中的和谐理念，追求人与人、人与自然的和谐。",
            "category": "philosophy",
            "tags": ["harmony", "peace", "balance"],
            "tattoo_popularity": 85,
            "recommended_positions": ["back", "chest", "wrist"],
            "is_verified": True
        },
        {
            "character": "福",
            "pinyin": "fú",
            "meaning_en": "fortune, blessing",
            "meaning_zh": "福气，幸福，福运",
            "cultural_context": "代表好运和幸福，是中国传统文化中最受欢迎的吉祥字。",
            "category": "fortune",
            "tags": ["fortune", "blessing", "luck"],
            "tattoo_popularity": 98,
            "recommended_positions": ["back", "chest", "shoulder"],
            "is_verified": True
        }
    ]
    
    for char_data in sample_characters:
        # 检查是否已存在
        existing = db.query(ChineseCharacter).filter(
            ChineseCharacter.character == char_data["character"]
        ).first()
        
        if not existing:
            character = ChineseCharacter(**char_data)
            db.add(character)
    
    db.commit()

def init_sample_idioms(db: Session):
    """初始化示例成语数据"""
    
    sample_idioms = [
        {
            "idiom": "自强不息",
            "pinyin": "zì qiáng bù xī",
            "meaning_en": "constantly strive to become stronger",
            "meaning_zh": "自己努力向上，永不懈怠",
            "origin_story": "出自《周易·乾》：'天行健，君子以自强不息。'",
            "cultural_context": "体现了中华民族不断进取、永不放弃的精神品质。",
            "category": "motivation",
            "tags": ["self-improvement", "perseverance", "strength"],
            "character_count": 4,
            "difficulty_level": 2,
            "tattoo_popularity": 90,
            "recommended_positions": ["back", "chest", "forearm"],
            "is_verified": True
        },
        {
            "idiom": "厚德载物",
            "pinyin": "hòu dé zài wù",
            "meaning_en": "virtue carries all things",
            "meaning_zh": "以深厚的德泽育人利物",
            "origin_story": "出自《周易·坤》：'地势坤，君子以厚德载物。'",
            "cultural_context": "强调品德修养的重要性，是中华文化的核心价值观。",
            "category": "virtue",
            "tags": ["virtue", "morality", "character"],
            "character_count": 4,
            "difficulty_level": 3,
            "tattoo_popularity": 85,
            "recommended_positions": ["back", "chest"],
            "is_verified": True
        },
        {
            "idiom": "知行合一",
            "pinyin": "zhī xíng hé yī",
            "meaning_en": "unity of knowledge and action",
            "meaning_zh": "认识与实践相结合",
            "origin_story": "明代哲学家王阳明提出的重要思想。",
            "cultural_context": "强调理论与实践相结合，是中国哲学的重要理念。",
            "category": "philosophy",
            "tags": ["knowledge", "action", "philosophy"],
            "character_count": 4,
            "difficulty_level": 3,
            "tattoo_popularity": 78,
            "recommended_positions": ["forearm", "back"],
            "is_verified": True
        }
    ]
    
    for idiom_data in sample_idioms:
        # 检查是否已存在
        existing = db.query(ChineseIdiom).filter(
            ChineseIdiom.idiom == idiom_data["idiom"]
        ).first()
        
        if not existing:
            idiom = ChineseIdiom(**idiom_data)
            db.add(idiom)
    
    db.commit()

def init_tattoo_styles(db: Session):
    """初始化纹身风格数据"""
    
    sample_styles = [
        {
            "name": "calligraphy",
            "name_zh": "书法体",
            "description": "Traditional Chinese calligraphy style with elegant brush strokes",
            "characteristics": {
                "style": "traditional",
                "brush_strokes": "elegant",
                "thickness": "varied"
            },
            "suitable_positions": ["forearm", "back", "chest", "shoulder"],
            "difficulty_level": 2,
            "prompt_template": "Chinese calligraphy tattoo of '{content}' in traditional brush style, elegant strokes, black ink, suitable for {position}, artistic, traditional Chinese art style",
            "is_premium": False
        },
        {
            "name": "modern",
            "name_zh": "现代简约",
            "description": "Clean, modern interpretation of Chinese characters",
            "characteristics": {
                "style": "minimalist",
                "lines": "clean",
                "thickness": "uniform"
            },
            "suitable_positions": ["wrist", "ankle", "neck", "forearm"],
            "difficulty_level": 1,
            "prompt_template": "Modern minimalist Chinese character '{content}' tattoo design, clean lines, simple style, black ink, suitable for {position}",
            "is_premium": False
        },
        {
            "name": "watercolor",
            "name_zh": "水彩风格",
            "description": "Artistic watercolor style with flowing colors",
            "characteristics": {
                "style": "artistic",
                "colors": "flowing",
                "effect": "watercolor"
            },
            "suitable_positions": ["shoulder", "back", "thigh"],
            "difficulty_level": 4,
            "prompt_template": "Watercolor style Chinese character '{content}' tattoo, flowing colors, artistic brush effects, suitable for {position}, colorful, artistic",
            "is_premium": True
        },
        {
            "name": "tribal",
            "name_zh": "部落风格",
            "description": "Bold tribal interpretation of Chinese characters",
            "characteristics": {
                "style": "bold",
                "lines": "thick",
                "pattern": "tribal"
            },
            "suitable_positions": ["arm", "leg", "back"],
            "difficulty_level": 3,
            "prompt_template": "Tribal style Chinese character '{content}' tattoo design, bold thick lines, tribal patterns, black ink, suitable for {position}",
            "is_premium": True
        }
    ]
    
    for style_data in sample_styles:
        # 检查是否已存在
        existing = db.query(TattooStyle).filter(
            TattooStyle.name == style_data["name"]
        ).first()
        
        if not existing:
            style = TattooStyle(**style_data)
            db.add(style)
    
    db.commit()

def initialize_database(db: Session):
    """初始化数据库数据"""
    print("正在初始化数据库数据...")
    
    init_sample_characters(db)
    print("✓ 汉字数据初始化完成")
    
    init_sample_idioms(db)
    print("✓ 成语数据初始化完成")
    
    init_tattoo_styles(db)
    print("✓ 纹身风格数据初始化完成")
    
    print("数据库初始化完成！")
