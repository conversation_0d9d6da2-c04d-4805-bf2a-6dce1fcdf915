#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库表结构
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db.database import engine


def fix_tattoo_requests_table():
    """修复tattoo_requests表结构"""

    print("🔧 修复tattoo_requests表结构...")

    with engine.connect() as conn:
        # 检查表结构
        print("📋 检查当前表结构...")
        result = conn.execute(text("DESCRIBE tattoo_requests"))
        columns = [row[0] for row in result.fetchall()]
        print(f"当前字段: {columns}")

        # 添加缺失的字段
        missing_fields = []

        if "keywords" not in columns:
            missing_fields.append("ADD COLUMN keywords JSON")

        if "recommended_characters" not in columns:
            missing_fields.append("ADD COLUMN recommended_characters JSON")

        if "recommended_idioms" not in columns:
            missing_fields.append("ADD COLUMN recommended_idioms JSON")

        if "ai_explanation" not in columns:
            missing_fields.append("ADD COLUMN ai_explanation TEXT")

        if "updated_at" not in columns:
            missing_fields.append("ADD COLUMN updated_at TIMESTAMP NULL")

        if "personal_meaning" not in columns:
            missing_fields.append("ADD COLUMN personal_meaning TEXT")

        if "size_preference" not in columns:
            missing_fields.append("ADD COLUMN size_preference VARCHAR(50)")

        if missing_fields:
            print(f"🔨 添加缺失字段: {missing_fields}")

            for field in missing_fields:
                try:
                    sql = f"ALTER TABLE tattoo_requests {field}"
                    print(f"执行: {sql}")
                    conn.execute(text(sql))
                    conn.commit()
                    print(f"✅ 成功添加字段")
                except Exception as e:
                    print(f"❌ 添加字段失败: {e}")
        else:
            print("✅ 表结构已经完整")

        # 再次检查表结构
        print("📋 修复后的表结构...")
        result = conn.execute(text("DESCRIBE tattoo_requests"))
        for row in result.fetchall():
            print(f"  {row[0]}: {row[1]}")


def fix_tattoo_images_table():
    """修复tattoo_images表结构"""

    print("\n🔧 检查tattoo_images表结构...")

    with engine.connect() as conn:
        try:
            # 检查表是否存在
            result = conn.execute(text("SHOW TABLES LIKE 'tattoo_images'"))
            if not result.fetchone():
                print("📋 创建tattoo_images表...")
                create_sql = """
                CREATE TABLE tattoo_images (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    request_id INT,
                    image_url VARCHAR(500) NOT NULL,
                    thumbnail_url VARCHAR(500),
                    watermarked_url VARCHAR(500),
                    style VARCHAR(50) NOT NULL,
                    position VARCHAR(50) NOT NULL,
                    content VARCHAR(100) NOT NULL,
                    width INT,
                    height INT,
                    file_size INT,
                    generation_prompt TEXT,
                    generation_model VARCHAR(50),
                    generation_time FLOAT,
                    is_high_resolution BOOLEAN DEFAULT FALSE,
                    is_watermarked BOOLEAN DEFAULT TRUE,
                    quality_score FLOAT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (request_id) REFERENCES tattoo_requests(id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """
                conn.execute(text(create_sql))
                conn.commit()
                print("✅ tattoo_images表创建成功")
            else:
                print("✅ tattoo_images表已存在")
        except Exception as e:
            print(f"❌ 处理tattoo_images表失败: {e}")


if __name__ == "__main__":
    try:
        fix_tattoo_requests_table()
        fix_tattoo_images_table()
        print("\n🎉 数据库表结构修复完成！")
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback

        traceback.print_exc()
