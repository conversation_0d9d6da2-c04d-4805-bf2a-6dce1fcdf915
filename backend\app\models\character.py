"""
汉字和成语数据模型
Chinese Character and Idiom Data Model
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON
from sqlalchemy.sql import func
from app.db.database import Base


class ChineseCharacter(Base):
    """汉字模型"""

    __tablename__ = "chinese_characters"

    id = Column(Integer, primary_key=True, index=True)
    char_text = Column(String(10), unique=True, index=True, nullable=False)
    pinyin = Column(String(50), nullable=False)

    # 含义和解释
    meaning_en = Column(Text, nullable=False)  # 英文含义
    meaning_zh = Column(Text, nullable=False)  # 中文含义
    cultural_context = Column(Text, nullable=True)  # 文化背景
    usage_examples = Column(JSON, nullable=True)  # 使用示例

    # 分类标签
    category = Column(String(50), nullable=True)  # 分类：情感、品质、自然等
    tags = Column(JSON, nullable=True)  # 标签数组

    # 纹身适用性
    tattoo_popularity = Column(Integer, default=0)  # 纹身流行度评分
    recommended_positions = Column(JSON, nullable=True)  # 推荐纹身位置

    # 状态
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)  # 是否经过文化专家验证

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<ChineseCharacter(id={self.id}, char_text='{self.char_text}', pinyin='{self.pinyin}')>"


class ChineseIdiom(Base):
    """成语模型"""

    __tablename__ = "chinese_idioms"

    id = Column(Integer, primary_key=True, index=True)
    idiom = Column(String(50), unique=True, index=True, nullable=False)
    pinyin = Column(String(200), nullable=False)

    # 含义和解释
    meaning_en = Column(Text, nullable=False)  # 英文含义
    meaning_zh = Column(Text, nullable=False)  # 中文含义
    origin_story = Column(Text, nullable=True)  # 典故来源
    cultural_context = Column(Text, nullable=True)  # 文化背景
    usage_examples = Column(JSON, nullable=True)  # 使用示例

    # 分类标签
    category = Column(String(50), nullable=True)  # 分类
    tags = Column(JSON, nullable=True)  # 标签数组
    difficulty_level = Column(Integer, default=1)  # 难度等级 1-5

    # 纹身适用性
    tattoo_popularity = Column(Integer, default=0)  # 纹身流行度评分
    recommended_positions = Column(JSON, nullable=True)  # 推荐纹身位置
    character_count = Column(Integer, nullable=False)  # 字符数量

    # 状态
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)  # 是否经过文化专家验证

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<ChineseIdiom(id={self.id}, idiom='{self.idiom}', pinyin='{self.pinyin}')>"
