// 用户状态管理
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { AuthService } from '@/services/auth'
import type { User, LoginRequest, RegisterRequest } from '@/types'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  const isAuthenticated = computed(() => !!user.value)

  // 初始化用户状态
  function initializeUser() {
    const localUser = AuthService.getLocalUser()
    if (localUser && AuthService.isAuthenticated()) {
      user.value = localUser
    }
  }

  // 用户登录
  async function login(credentials: LoginRequest) {
    isLoading.value = true
    try {
      const response = await AuthService.login(credentials)
      user.value = response.user
      ElMessage.success('登录成功')
      return response
    } catch (error) {
      ElMessage.error('登录失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 用户注册
  async function register(userData: RegisterRequest) {
    isLoading.value = true
    try {
      const newUser = await AuthService.register(userData)
      ElMessage.success('注册成功，请登录')
      return newUser
    } catch (error) {
      ElMessage.error('注册失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 获取当前用户信息
  async function fetchCurrentUser() {
    if (!AuthService.isAuthenticated()) return

    try {
      const currentUser = await AuthService.getCurrentUser()
      user.value = currentUser
      return currentUser
    } catch (error) {
      console.error('获取用户信息失败:', error)
      logout()
    }
  }

  // 用户登出
  function logout() {
    AuthService.logout()
    user.value = null
    ElMessage.success('已退出登录')
  }

  // 更新用户积分
  function updateCredits(newCredits: number) {
    if (user.value) {
      user.value.credits = newCredits
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  return {
    user,
    isLoading,
    isAuthenticated,
    initializeUser,
    login,
    register,
    fetchCurrentUser,
    logout,
    updateCredits
  }
})
