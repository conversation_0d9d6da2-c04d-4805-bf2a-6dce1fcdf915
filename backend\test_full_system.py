#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的AI推荐系统
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_service_new import AIService

async def test_full_system():
    """测试完整的AI推荐系统"""
    
    print("🔍 测试完整的AI推荐系统...")
    
    # 创建AI服务实例
    ai_service = AIService()
    
    # 测试参数
    keywords = ["勇气", "智慧"]
    description = "我想要一个代表勇气和智慧的纹身"
    max_results = 3
    
    try:
        print(f"📝 关键词: {keywords}")
        print(f"📝 描述: {description}")
        print(f"📊 要求数量: {max_results}")
        print("🚀 调用AI推荐系统...")
        
        # 调用AI推荐
        result = await ai_service.get_recommendations(
            keywords=keywords,
            description=description,
            recommendation_type="both",
            max_results=max_results,
            db=None
        )
        
        print("✅ AI推荐系统调用成功！")
        print("📋 推荐结果:")
        print(f"   汉字数量: {len(result.characters)}")
        print(f"   成语数量: {len(result.idioms)}")
        
        # 显示汉字推荐
        print("\n🔤 汉字推荐:")
        for i, char in enumerate(result.characters, 1):
            print(f"   {i}. {char.character} ({char.pinyin})")
            print(f"      含义: {char.meaning_zh}")
            print(f"      评分: {char.score}")
        
        # 显示成语推荐
        print("\n📚 成语推荐:")
        for i, idiom in enumerate(result.idioms, 1):
            print(f"   {i}. {idiom.idiom} ({idiom.pinyin})")
            print(f"      含义: {idiom.meaning_zh}")
            print(f"      评分: {idiom.score}")
        
        print(f"\n💬 AI解释: {result.ai_explanation}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI推荐系统调用失败: {e}")
        print("🔧 请检查:")
        print("   1. API密钥是否正确配置")
        print("   2. 网络连接是否正常")
        print("   3. AI服务是否可用")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_full_system())
    if success:
        print("\n🎉 系统测试成功！可以启动后端服务了。")
    else:
        print("\n❌ 系统测试失败，请检查配置。")
