<template>
  <div class="home-page">
    <!-- 简化的英雄区域 -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title">
              <span class="chinese-title">汉字纹身AI</span>
              <span class="english-title">Chinese Character Tattoo AI</span>
            </h1>
            <p class="hero-description">
              告诉我们您的想法，AI为您推荐最合适的汉字，并生成专属纹身设计
            </p>
            <div class="hero-actions">
              <el-button
                type="primary"
                size="large"
                @click="$router.push('/recommend')"
                class="cta-button"
              >
                <el-icon><MagicStick /></el-icon>
                开始AI推荐
              </el-button>
            </div>
          </div>

          <div class="hero-visual">
            <div class="demo-screenshot">
              <img src="/demo-screenshot.png" alt="平台演示截图" />
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { MagicStick } from '@element-plus/icons-vue'
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 英雄区域 */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.hero-title {
  margin: 0 0 24px 0;
}

.chinese-title {
  display: block;
  font-size: 3.5rem;
  font-weight: 700;
  color: #ffd700;
  font-family: 'SimSun', serif;
  margin-bottom: 8px;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.english-title {
  display: block;
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 2px;
}

.hero-description {
  font-size: 1.2rem;
  line-height: 1.8;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 32px;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 16px;
}

.cta-button {
  padding: 16px 40px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 30px;
  box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

/* 演示截图区域 */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.demo-screenshot {
  width: 100%;
  max-width: 600px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 215, 0, 0.2);
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.demo-screenshot img {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .chinese-title {
    font-size: 2.5rem;
  }
  
  .english-title {
    font-size: 1.2rem;
  }
  
  .hero-description {
    font-size: 1.1rem;
    max-width: none;
  }
  
  .demo-screenshot {
    max-width: 400px;
  }
}

@media (max-width: 480px) {
  .chinese-title {
    font-size: 2rem;
  }
  
  .cta-button {
    padding: 14px 32px;
    font-size: 16px;
  }
  
  .demo-screenshot {
    max-width: 300px;
  }
}
</style>
