/*! Element Plus v2.9.11 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleUk = factory());
})(this, (function () { 'use strict';

  var uk = {
    name: "uk",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "OK",
        clear: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438"
      },
      datepicker: {
        now: "\u0417\u0430\u0440\u0430\u0437",
        today: "\u0421\u044C\u043E\u0433\u043E\u0434\u043D\u0456",
        cancel: "\u0412\u0456\u0434\u043C\u0456\u043D\u0430",
        clear: "\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438",
        confirm: "OK",
        selectDate: "\u0412\u0438\u0431\u0440\u0430\u0442\u0438 \u0434\u0430\u0442\u0443",
        selectTime: "\u0412\u0438\u0431\u0440\u0430\u0442\u0438 \u0447\u0430\u0441",
        startDate: "\u0414\u0430\u0442\u0430 \u043F\u043E\u0447\u0430\u0442\u043A\u0443",
        startTime: "\u0427\u0430\u0441 \u043F\u043E\u0447\u0430\u0442\u043A\u0443",
        endDate: "\u0414\u0430\u0442\u0430 \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u043D\u044F",
        endTime: "\u0427\u0430\u0441 \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043D\u043D\u044F",
        prevYear: "\u041F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u0456\u0439 \u0420\u0456\u043A",
        nextYear: "\u041D\u0430\u0441\u0442\u0443\u043F\u043D\u0438\u0439 \u0420\u0456\u043A",
        prevMonth: "\u041F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u0456\u0439 \u041C\u0456\u0441\u044F\u0446\u044C",
        nextMonth: "\u041D\u0430\u0441\u0442\u0443\u043F\u043D\u0438\u0439 \u041C\u0456\u0441\u044F\u0446\u044C",
        year: "",
        month1: "\u0421\u0456\u0447\u0435\u043D\u044C",
        month2: "\u041B\u044E\u0442\u0438\u0439",
        month3: "\u0411\u0435\u0440\u0435\u0437\u0435\u043D\u044C",
        month4: "\u041A\u0432\u0456\u0442\u0435\u043D\u044C",
        month5: "\u0422\u0440\u0430\u0432\u0435\u043D\u044C",
        month6: "\u0427\u0435\u0440\u0432\u0435\u043D\u044C",
        month7: "\u041B\u0438\u043F\u0435\u043D\u044C",
        month8: "\u0421\u0435\u0440\u043F\u0435\u043D\u044C",
        month9: "\u0412\u0435\u0440\u0435\u0441\u0435\u043D\u044C",
        month10: "\u0416\u043E\u0432\u0442\u0435\u043D\u044C",
        month11: "\u041B\u0438\u0441\u0442\u043E\u043F\u0430\u0434",
        month12: "\u0413\u0440\u0443\u0434\u0435\u043D\u044C",
        week: "\u0442\u0438\u0436\u0434\u0435\u043D\u044C",
        weeks: {
          sun: "\u041D\u0434",
          mon: "\u041F\u043D",
          tue: "\u0412\u0442",
          wed: "\u0421\u0440",
          thu: "\u0427\u0442",
          fri: "\u041F\u0442",
          sat: "\u0421\u0431"
        },
        months: {
          jan: "\u0421\u0456\u0447",
          feb: "\u041B\u044E\u0442",
          mar: "\u0411\u0435\u0440",
          apr: "\u041A\u0432\u0456",
          may: "\u0422\u0440\u0430",
          jun: "\u0427\u0435\u0440",
          jul: "\u041B\u0438\u043F",
          aug: "\u0421\u0435\u0440",
          sep: "\u0412\u0435\u0440",
          oct: "\u0416\u043E\u0432",
          nov: "\u041B\u0438\u0441",
          dec: "\u0413\u0440\u0443"
        }
      },
      select: {
        loading: "\u0417\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0435\u043D\u043D\u044F",
        noMatch: "\u0421\u043F\u0456\u0432\u043F\u0430\u0434\u0456\u043D\u044C \u043D\u0435 \u0437\u043D\u0430\u0439\u0434\u0435\u043D\u043E",
        noData: "\u041D\u0435\u043C\u0430\u0454 \u0434\u0430\u043D\u0438\u0445",
        placeholder: "\u041E\u0431\u0440\u0430\u0442\u0438"
      },
      mention: {
        loading: "\u0417\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0435\u043D\u043D\u044F"
      },
      cascader: {
        noMatch: "\u0421\u043F\u0456\u0432\u043F\u0430\u0434\u0456\u043D\u044C \u043D\u0435 \u0437\u043D\u0430\u0439\u0434\u0435\u043D\u043E",
        loading: "\u0417\u0430\u0432\u0430\u043D\u0442\u0430\u0436\u0435\u043D\u043D\u044F",
        placeholder: "\u041E\u0431\u0440\u0430\u0442\u0438",
        noData: "\u041D\u0435\u043C\u0430\u0454 \u0434\u0430\u043D\u0438\u0445"
      },
      pagination: {
        goto: "\u041F\u0435\u0440\u0435\u0439\u0442\u0438",
        pagesize: "\u043D\u0430 \u0441\u0442\u043E\u0440\u0456\u043D\u0446\u0456",
        total: "\u0412\u0441\u044C\u043E\u0433\u043E {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "\u041F\u043E\u0432\u0456\u0434\u043E\u043C\u043B\u0435\u043D\u043D\u044F",
        confirm: "OK",
        cancel: "\u0412\u0456\u0434\u043C\u0456\u043D\u0430",
        error: "\u041D\u0435\u043F\u0440\u0438\u043F\u0443\u0441\u0442\u0438\u043C\u0438\u0439 \u0432\u0432\u0456\u0434 \u0434\u0430\u043D\u0438\u0445"
      },
      upload: {
        deleteTip: "\u043D\u0430\u0442\u0438\u0441\u043D\u0456\u0442\u044C \u043A\u043D\u043E\u043F\u043A\u0443 \u0449\u043E\u0431 \u0432\u0438\u0434\u0430\u043B\u0438\u0442\u0438",
        delete: "\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438",
        preview: "\u041F\u0435\u0440\u0435\u0433\u043B\u044F\u0434",
        continue: "\u041F\u0440\u043E\u0434\u043E\u0432\u0436\u0438\u0442\u0438"
      },
      table: {
        emptyText: "\u041D\u0435\u043C\u0430\u0454 \u0434\u0430\u043D\u0438\u0445",
        confirmFilter: "\u041F\u0456\u0434\u0442\u0432\u0435\u0440\u0434\u0438\u0442\u0438",
        resetFilter: "\u0421\u043A\u0438\u043D\u0443\u0442\u0438",
        clearFilter: "\u0412\u0441\u0435",
        sumText: "\u0421\u0443\u043C\u0430"
      },
      tour: {
        next: "\u0414\u0430\u043B\u0456",
        previous: "\u041D\u0430\u0437\u0430\u0434",
        finish: "\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438"
      },
      tree: {
        emptyText: "\u041D\u0435\u043C\u0430\u0454 \u0434\u0430\u043D\u0438\u0445"
      },
      transfer: {
        noMatch: "\u0421\u043F\u0456\u0432\u043F\u0430\u0434\u0456\u043D\u044C \u043D\u0435 \u0437\u043D\u0430\u0439\u0434\u0435\u043D\u043E",
        noData: "\u041E\u0431\u0440\u0430\u0442\u0438",
        titles: ["\u0421\u043F\u0438\u0441\u043E\u043A 1", "\u0421\u043F\u0438\u0441\u043E\u043A 2"],
        filterPlaceholder: "\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u043A\u043B\u044E\u0447\u043E\u0432\u0435 \u0441\u043B\u043E\u0432\u043E",
        noCheckedFormat: "{total} \u043F\u0443\u043D\u043A\u0442\u0456\u0432",
        hasCheckedFormat: "{checked}/{total} \u0432\u0438\u0431\u0440\u0430\u043D\u043E"
      },
      image: {
        error: "FAILED"
      },
      pageHeader: {
        title: "Back"
      },
      popconfirm: {
        confirmButtonText: "Yes",
        cancelButtonText: "No"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return uk;

}));
